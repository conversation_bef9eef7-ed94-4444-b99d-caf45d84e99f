chain_client:
  # 链ID
  chain_id: "chain1"
  # 组织ID
  org_id: "wx-org1.chainmaker.org"
  # 客户端用户交易签名私钥路径
  user_sign_key_file_path: "./testdata/crypto-config/wx-org1.chainmaker.org/admin/admin.key"
  # 客户端用户交易签名私钥密码(无密码则不需要设置)
  # user_sign_key_pwd: "123"
  # 客户端用户私钥路径，用于tls连接
  # user_key_file_path: "./testdata/crypto-config/wx-org1.chainmaker.org/user/client1/client1.tls.key"
  # 客户端用户私钥密码(无密码则不需要设置)
  # user_key_pwd: "123"
  # 客户端用户证书路径，用于tls连接
  # user_crt_file_path: "./testdata/crypto-config/wx-org1.chainmaker.org/user/client1/client1.tls.crt"
  # 客户端用户加密私钥路径(tls加密证书对应私钥，应用于国密GMTLS双证书体系；若未设置仅使用单证书）
  # user_enc_key_file_path: "./testdata/crypto-config/wx-org1.chainmaker.org/user/client1/client1.tls.enc.key"
  # 客户端用户加密私钥密码(无密码则不需要设置)
  # user_enc_key_pwd: "123"
  # 客户端用户加密证书路径(tls加密证书，应用于国密GMTLS双证书体系；若未设置仅使用单证书）
  # user_enc_crt_file_path: "./testdata/crypto-config/wx-org1.chainmaker.org/user/client1/client1.tls.enc.crt"
  # 签名使用的哈希算法，和节点保持一直
  crypto:
    hash: SHA256
  auth_type: permissionedWithKey
  # 节点是否仅支持异步返回的方式，默认为false，表示可以支持同步，仅有老版本的底链才仅支持（小于v2.3.3）
  node_only_async: false
  # 同步交易结果模式下，轮询获取交易结果时的最大轮询次数，删除此项或设为<=0则使用默认值 10
  retry_limit: 20
  # 同步交易结果模式下，每次轮询交易结果时的等待时间，单位：ms 删除此项或设为<=0则使用默认值 500
  retry_interval: 500
  # txid配置项：默认支持TimestampKey，如果开启enableNormalKey则使用NormalKey
  enable_normal_key: false
  # 代理地址，置空为不使用代理
  # 格式为：http://[username:password@]proxyhost:proxyport，如：http://myproxy:8080
  # proxy:
  # 优化连接检查周期，单位：s，默认开启，每隔1分钟检查一次，但仅当nodes为多个时生效
  # 该值生效时，会自动获取多个node中的区块高度，并按照高度和消耗时间排序，以供用户实际使用时优先获取到最优连接
  # 该值如果设置为0，则会使用默认值：60s，如需关闭，需将该值设置为-1
  optimize_detection: 60
  # 关闭订阅区块及合约事件时的乐观处理，默认为false，表示开启，true表示关闭
  # 该值需要结合optimize_detection使用，在optimize_detection生效时该值会在默认订阅时触发
  disable_subscribe_optimize: false

  nodes:
    - # 节点地址，格式为：IP:端口:连接数
      node_addr: "127.0.0.1:12301"
      # 节点连接数
      conn_cnt: 10
      # RPC连接是否启用双向TLS认证
      enable_tls: false
      # 信任证书池路径
      trust_root_paths:
        - "./testdata/crypto-config/wx-org1.chainmaker.org/ca"
      # TLS hostname
      tls_host_name: "chainmaker.org"
      # 转发分流配置，根据配置的参数，在nginx中配置X-Server-Name的value值，分流到不同的机器或者集群上
      chain_tls_host_name: ""

  archive:
    # 数据归档链外存储相关配置
    # 如果使用了新版本的归档中心,这个地方配置为archivecenter
    type: "mysql" # archivecenter 归档中心, mysql mysql数据库
    dest: "root:123456:localhost:3306"
    secret_key: xxx

  rpc_client:
    max_receive_message_size: 100 # grpc客户端接收消息时，允许单条message大小的最大值(MB)
    max_send_message_size: 100 # grpc客户端发送消息时，允许单条message大小的最大值(MB)
    send_tx_timeout: 60 # grpc 客户端发送交易超时时间
    get_tx_timeout: 60 # rpc 客户端查询交易超时时间
  # #如果启用了归档中心,可以打开下面的归档中心配置
  archive_center_query_first: false # 如果为true且归档中心配置打开,那么查询数据优先从归档中心查询
    # archive_center_config:
    #   chain_genesis_hash: c670b598127f5795767d1acbae435e714797596f7e0a55dd05205da948de1a0a
    #   archive_center_http_url: http://127.0.0.1:13119
    #   request_second_limit: 10
    #   rpc_address: 127.0.0.1:13120
    #   tls_enable: false
    #   tls:
    #     server_name: archiveserver1.tls.wx-org.chainmaker.org
    #     priv_key_file: ./testdata/archivecenter/archiveclient1.tls.key
    #     cert_file: ./testdata/archivecenter/archiveclient1.tls.crt
    #     trust_ca_list:
    #       - ./testdata/archivecenter/ca.crt

  #   max_send_msg_size: 200
  #   max_recv_msg_size: 200