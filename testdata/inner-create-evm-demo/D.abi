[{"inputs": [{"internalType": "uint256", "name": "a", "type": "uint256"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [], "name": "get", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "x", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}]