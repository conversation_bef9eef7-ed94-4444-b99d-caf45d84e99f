run:
  timeout: 10m
  tests: true
  # default is true. Enables skipping of directories:
  #   vendor$, third_party$, testdata$, examples$, Godeps$, builtin$

  go: "1.15"

linters:
  disable-all: true
  enable:
    #- goconst
    - gocyclo
    - gofmt
    - ineffassign
    - staticcheck
    - typecheck
    - goimports
    - revive
    - gosimple
    - govet
    - lll
    - rowserrcheck
    - misspell
    - unused
    - sqlclosecheck
    - gosec


linters-settings:
  goconst:
    min-len: 2
    min-occurrences: 2
  gocyclo:
    min-complexity: 60
  goimports:
  #local-prefixes: git.code.oa.com
  golint:
  #min-confidence: 0
  govet:
    check-shadowing: false
  lll:
    line-length: 120
  errcheck:
    check-type-assertions: true
  gocritic:
    enabled-checks:
      - nestingReduce
    settings:
      nestingReduce:
        bodyWidth: 5
  revive:
    # see https://github.com/mgechev/revive#available-rules for details.
    rules:
      #      - name: blank-imports
      - name: context-as-argument
      - name: context-keys-type
      - name: dot-imports
      - name: error-return
      - name: error-strings
      - name: error-naming
      - name: exported
      - name: if-return
      - name: increment-decrement
      #      - name: var-naming
      - name: var-declaration
      - name: package-comments
      - name: range
      #      - name: receiver-naming
      - name: time-naming
      - name: unexported-return
      - name: indent-error-flow
      - name: errorf
      - name: empty-block
      - name: superfluous-else
      - name: unexported-naming
      - name: unexported-return
      - name: unreachable-code
      - name: redefines-builtin-id
      - name: function-length
        arguments: [ 100,0 ]

issues:
  exclude-use-default: true
  include:
    - EXC0004 # govet (possible misuse of unsafe.Pointer|should have signature)
    - EXC0005 # staticcheck ineffective break statement. Did you mean to break out of the outer loop
    - EXC0012 # revive exported (method|function|type|const) (.+) should have comment or be unexported
    - EXC0013 # revive package comment should be of the form "(.+)...
    - EXC0014 # revive comment on exported (.+) should be of the form "(.+)..."
    - EXC0015 # revive should have a package comment, unless it's in another file for this package

  exclude-rules:
    - path: .*_test.go
      linters:
        - unused
        - deadcode
        - ineffassign
        - staticcheck
        - forbidigo
        - dupl
        - errcheck
        - lll
        - funlen

  max-same-issues: 0
  new: false
  max-issues-per-linter: 0
  #  skip-files:
  exclude-dirs:
    - examples #test
    - testdata #test

output:
  sort-results: true
