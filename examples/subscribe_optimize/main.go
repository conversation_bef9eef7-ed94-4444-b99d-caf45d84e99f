/*
Copyright (C) BABEC. All rights reserved.
Copyright (C) THL A29 Limited, a Tencent company. All rights reserved.

SPDX-License-Identifier: Apache-2.0
*/

package main

import (
	"context"
	"fmt"
	"log"

	"chainmaker.org/chainmaker/pb-go/v2/common"
	sdk "chainmaker.org/chainmaker/sdk-go/v2"
	"chainmaker.org/chainmaker/sdk-go/v2/examples"
)

const (
	sdkConfigOrg1Client1Path = "../sdk_configs/sdk_config_optimize_client1.yml"
)

func main() {
	client, err := examples.CreateChainClientWithSDKConf(sdkConfigOrg1Client1Path)
	if err != nil {
		log.Fatalln(err)
	}

	go testSubscribeBlockWithOptimize(client, false)
	go testSubscribeBlockWithOptimize(client, true)
	go testSubscribeContractEvent(client)
	select {}
}

func testSubscribeBlockWithOptimize(client *sdk.ChainClient, onlyHeader bool) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	c, err := client.SubscribeBlockWithOptimize(ctx, 0, 10, true, onlyHeader, 10, 30)
	if err != nil {
		log.Fatalln(err)
	}

	for {
		select {
		case block, ok := <-c:
			if !ok {
				fmt.Println("chan is close!")
				return
			}

			if block == nil {
				log.Fatalln("require not nil")
			}

			if onlyHeader {
				blockHeader, ok := block.(*common.BlockHeader)
				if !ok {
					log.Fatalln("require true")
				}

				fmt.Printf("recv blockHeader [%d] => %+v\n", blockHeader.BlockHeight, blockHeader)
			} else {
				blockInfo, ok := block.(*common.BlockInfo)
				if !ok {
					log.Fatalln("require true")
				}

				fmt.Printf("recv blockInfo [%d] => %+v\n", blockInfo.Block.Header.BlockHeight, blockInfo)
			}
		case <-ctx.Done():
			return
		}
	}
}

func testSubscribeContractEvent(client *sdk.ChainClient) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	c, err := client.SubscribeContractEventWithOptimize(ctx, 0, -1, "claim005", "", 10, 30)
	if err != nil {
		log.Fatalln(err)
	}

	for {
		select {
		case event, ok := <-c:
			if !ok {
				fmt.Println("chan is close!")
				return
			}
			if event == nil {
				log.Fatalln("require not nil")
			}
			contractEventInfo, ok := event.(*common.ContractEventInfoList)
			if !ok {
				log.Fatalln("require true")
			}
			fmt.Printf("recv contract event [%d] => %+v\n", contractEventInfo.ContractEvents[0].BlockHeight,
				contractEventInfo)
		case <-ctx.Done():
			return
		}
	}
}
