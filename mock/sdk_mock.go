// Code generated by MockGen. DO NOT EDIT.
// Source: sdk_interface.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	crypto "chainmaker.org/chainmaker/common/v2/crypto"
	accesscontrol "chainmaker.org/chainmaker/pb-go/v2/accesscontrol"
	common "chainmaker.org/chainmaker/pb-go/v2/common"
	config "chainmaker.org/chainmaker/pb-go/v2/config"
	discovery "chainmaker.org/chainmaker/pb-go/v2/discovery"
	store "chainmaker.org/chainmaker/pb-go/v2/store"
	syscontract "chainmaker.org/chainmaker/pb-go/v2/syscontract"
	txpool "chainmaker.org/chainmaker/pb-go/v2/txpool"
	chainmaker_sdk_go "chainmaker.org/chainmaker/sdk-go/v2"
	gomock "github.com/golang/mock/gomock"
)

// MockSDKInterface is a mock of SDKInterface interface.
type MockSDKInterface struct {
	ctrl     *gomock.Controller
	recorder *MockSDKInterfaceMockRecorder
}

// MockSDKInterfaceMockRecorder is the mock recorder for MockSDKInterface.
type MockSDKInterfaceMockRecorder struct {
	mock *MockSDKInterface
}

// NewMockSDKInterface creates a new mock instance.
func NewMockSDKInterface(ctrl *gomock.Controller) *MockSDKInterface {
	mock := &MockSDKInterface{ctrl: ctrl}
	mock.recorder = &MockSDKInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSDKInterface) EXPECT() *MockSDKInterfaceMockRecorder {
	return m.recorder
}

// AddAlias mocks base method.
func (m *MockSDKInterface) AddAlias() (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAlias")
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAlias indicates an expected call of AddAlias.
func (mr *MockSDKInterfaceMockRecorder) AddAlias() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAlias", reflect.TypeOf((*MockSDKInterface)(nil).AddAlias))
}

// AddCert mocks base method.
func (m *MockSDKInterface) AddCert() (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCert")
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCert indicates an expected call of AddCert.
func (mr *MockSDKInterfaceMockRecorder) AddCert() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCert", reflect.TypeOf((*MockSDKInterface)(nil).AddCert))
}

// ArchiveBlocks mocks base method.
func (m *MockSDKInterface) ArchiveBlocks(archiveHeight uint64, mode string, heightNoticeCallback func(chainmaker_sdk_go.ProcessMessage) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ArchiveBlocks", archiveHeight, mode, heightNoticeCallback)
	ret0, _ := ret[0].(error)
	return ret0
}

// ArchiveBlocks indicates an expected call of ArchiveBlocks.
func (mr *MockSDKInterfaceMockRecorder) ArchiveBlocks(archiveHeight, mode, heightNoticeCallback interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ArchiveBlocks", reflect.TypeOf((*MockSDKInterface)(nil).ArchiveBlocks), archiveHeight, mode, heightNoticeCallback)
}

// AttachGasLimit mocks base method.
func (m *MockSDKInterface) AttachGasLimit(payload *common.Payload, limit *common.Limit) *common.Payload {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AttachGasLimit", payload, limit)
	ret0, _ := ret[0].(*common.Payload)
	return ret0
}

// AttachGasLimit indicates an expected call of AttachGasLimit.
func (mr *MockSDKInterfaceMockRecorder) AttachGasLimit(payload, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AttachGasLimit", reflect.TypeOf((*MockSDKInterface)(nil).AttachGasLimit), payload, limit)
}

// CheckCallerCertAuth mocks base method.
func (m *MockSDKInterface) CheckCallerCertAuth(payload string, orgIds []string, signPairs []*syscontract.SignInfo) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCallerCertAuth", payload, orgIds, signPairs)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCallerCertAuth indicates an expected call of CheckCallerCertAuth.
func (mr *MockSDKInterfaceMockRecorder) CheckCallerCertAuth(payload, orgIds, signPairs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCallerCertAuth", reflect.TypeOf((*MockSDKInterface)(nil).CheckCallerCertAuth), payload, orgIds, signPairs)
}

// CreateArchiveBlockPayload mocks base method.
func (m *MockSDKInterface) CreateArchiveBlockPayload(targetBlockHeight uint64) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateArchiveBlockPayload", targetBlockHeight)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateArchiveBlockPayload indicates an expected call of CreateArchiveBlockPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateArchiveBlockPayload(targetBlockHeight interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateArchiveBlockPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateArchiveBlockPayload), targetBlockHeight)
}

// CreateCertManageFrozenPayload mocks base method.
func (m *MockSDKInterface) CreateCertManageFrozenPayload(certs []string) *common.Payload {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCertManageFrozenPayload", certs)
	ret0, _ := ret[0].(*common.Payload)
	return ret0
}

// CreateCertManageFrozenPayload indicates an expected call of CreateCertManageFrozenPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateCertManageFrozenPayload(certs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCertManageFrozenPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateCertManageFrozenPayload), certs)
}

// CreateCertManagePayload mocks base method.
func (m *MockSDKInterface) CreateCertManagePayload(method string, kvs []*common.KeyValuePair) *common.Payload {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCertManagePayload", method, kvs)
	ret0, _ := ret[0].(*common.Payload)
	return ret0
}

// CreateCertManagePayload indicates an expected call of CreateCertManagePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateCertManagePayload(method, kvs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCertManagePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateCertManagePayload), method, kvs)
}

// CreateCertManageRevocationPayload mocks base method.
func (m *MockSDKInterface) CreateCertManageRevocationPayload(certCrl string) *common.Payload {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCertManageRevocationPayload", certCrl)
	ret0, _ := ret[0].(*common.Payload)
	return ret0
}

// CreateCertManageRevocationPayload indicates an expected call of CreateCertManageRevocationPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateCertManageRevocationPayload(certCrl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCertManageRevocationPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateCertManageRevocationPayload), certCrl)
}

// CreateCertManageUnfrozenPayload mocks base method.
func (m *MockSDKInterface) CreateCertManageUnfrozenPayload(certs []string) *common.Payload {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCertManageUnfrozenPayload", certs)
	ret0, _ := ret[0].(*common.Payload)
	return ret0
}

// CreateCertManageUnfrozenPayload indicates an expected call of CreateCertManageUnfrozenPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateCertManageUnfrozenPayload(certs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCertManageUnfrozenPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateCertManageUnfrozenPayload), certs)
}

// CreateChainConfigAlterAddrTypePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigAlterAddrTypePayload(addrType string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigAlterAddrTypePayload", addrType)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigAlterAddrTypePayload indicates an expected call of CreateChainConfigAlterAddrTypePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigAlterAddrTypePayload(addrType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigAlterAddrTypePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigAlterAddrTypePayload), addrType)
}

// CreateChainConfigBlockUpdatePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigBlockUpdatePayload(txTimestampVerify, blockTimestampVerify bool, txTimeout, blockTimeout, blockTxCapacity, blockSize, blockInterval, txParamterSize uint32) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigBlockUpdatePayload", txTimestampVerify, blockTimestampVerify, txTimeout, blockTimeout, blockTxCapacity, blockSize, blockInterval, txParamterSize)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigBlockUpdatePayload indicates an expected call of CreateChainConfigBlockUpdatePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigBlockUpdatePayload(txTimestampVerify, blockTimestampVerify, txTimeout, blockTimeout, blockTxCapacity, blockSize, blockInterval, txParamterSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigBlockUpdatePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigBlockUpdatePayload), txTimestampVerify, blockTimestampVerify, txTimeout, blockTimeout, blockTxCapacity, blockSize, blockInterval, txParamterSize)
}

// CreateChainConfigConsensusExtAddPayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigConsensusExtAddPayload(kvs []*common.KeyValuePair) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigConsensusExtAddPayload", kvs)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigConsensusExtAddPayload indicates an expected call of CreateChainConfigConsensusExtAddPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigConsensusExtAddPayload(kvs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigConsensusExtAddPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigConsensusExtAddPayload), kvs)
}

// CreateChainConfigConsensusExtDeletePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigConsensusExtDeletePayload(keys []string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigConsensusExtDeletePayload", keys)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigConsensusExtDeletePayload indicates an expected call of CreateChainConfigConsensusExtDeletePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigConsensusExtDeletePayload(keys interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigConsensusExtDeletePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigConsensusExtDeletePayload), keys)
}

// CreateChainConfigConsensusExtUpdatePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigConsensusExtUpdatePayload(kvs []*common.KeyValuePair) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigConsensusExtUpdatePayload", kvs)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigConsensusExtUpdatePayload indicates an expected call of CreateChainConfigConsensusExtUpdatePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigConsensusExtUpdatePayload(kvs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigConsensusExtUpdatePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigConsensusExtUpdatePayload), kvs)
}

// CreateChainConfigConsensusNodeIdAddPayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigConsensusNodeIdAddPayload(nodeOrgId string, nodeIds []string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigConsensusNodeIdAddPayload", nodeOrgId, nodeIds)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigConsensusNodeIdAddPayload indicates an expected call of CreateChainConfigConsensusNodeIdAddPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigConsensusNodeIdAddPayload(nodeOrgId, nodeIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigConsensusNodeIdAddPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigConsensusNodeIdAddPayload), nodeOrgId, nodeIds)
}

// CreateChainConfigConsensusNodeIdDeletePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigConsensusNodeIdDeletePayload(nodeOrgId, nodeId string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigConsensusNodeIdDeletePayload", nodeOrgId, nodeId)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigConsensusNodeIdDeletePayload indicates an expected call of CreateChainConfigConsensusNodeIdDeletePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigConsensusNodeIdDeletePayload(nodeOrgId, nodeId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigConsensusNodeIdDeletePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigConsensusNodeIdDeletePayload), nodeOrgId, nodeId)
}

// CreateChainConfigConsensusNodeIdUpdatePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigConsensusNodeIdUpdatePayload(nodeOrgId, nodeOldNodeId, nodeNewNodeId string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigConsensusNodeIdUpdatePayload", nodeOrgId, nodeOldNodeId, nodeNewNodeId)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigConsensusNodeIdUpdatePayload indicates an expected call of CreateChainConfigConsensusNodeIdUpdatePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigConsensusNodeIdUpdatePayload(nodeOrgId, nodeOldNodeId, nodeNewNodeId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigConsensusNodeIdUpdatePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigConsensusNodeIdUpdatePayload), nodeOrgId, nodeOldNodeId, nodeNewNodeId)
}

// CreateChainConfigConsensusNodeOrgAddPayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigConsensusNodeOrgAddPayload(nodeOrgId string, nodeIds []string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigConsensusNodeOrgAddPayload", nodeOrgId, nodeIds)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigConsensusNodeOrgAddPayload indicates an expected call of CreateChainConfigConsensusNodeOrgAddPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigConsensusNodeOrgAddPayload(nodeOrgId, nodeIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigConsensusNodeOrgAddPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigConsensusNodeOrgAddPayload), nodeOrgId, nodeIds)
}

// CreateChainConfigConsensusNodeOrgDeletePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigConsensusNodeOrgDeletePayload(nodeOrgId string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigConsensusNodeOrgDeletePayload", nodeOrgId)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigConsensusNodeOrgDeletePayload indicates an expected call of CreateChainConfigConsensusNodeOrgDeletePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigConsensusNodeOrgDeletePayload(nodeOrgId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigConsensusNodeOrgDeletePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigConsensusNodeOrgDeletePayload), nodeOrgId)
}

// CreateChainConfigConsensusNodeOrgUpdatePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigConsensusNodeOrgUpdatePayload(nodeOrgId string, nodeIds []string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigConsensusNodeOrgUpdatePayload", nodeOrgId, nodeIds)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigConsensusNodeOrgUpdatePayload indicates an expected call of CreateChainConfigConsensusNodeOrgUpdatePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigConsensusNodeOrgUpdatePayload(nodeOrgId, nodeIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigConsensusNodeOrgUpdatePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigConsensusNodeOrgUpdatePayload), nodeOrgId, nodeIds)
}

// CreateChainConfigCoreUpdatePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigCoreUpdatePayload(txSchedulerTimeout, txSchedulerValidateTimeout uint64) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigCoreUpdatePayload", txSchedulerTimeout, txSchedulerValidateTimeout)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigCoreUpdatePayload indicates an expected call of CreateChainConfigCoreUpdatePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigCoreUpdatePayload(txSchedulerTimeout, txSchedulerValidateTimeout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigCoreUpdatePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigCoreUpdatePayload), txSchedulerTimeout, txSchedulerValidateTimeout)
}

// CreateChainConfigEnableOrDisableGasPayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigEnableOrDisableGasPayload() (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigEnableOrDisableGasPayload")
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigEnableOrDisableGasPayload indicates an expected call of CreateChainConfigEnableOrDisableGasPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigEnableOrDisableGasPayload() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigEnableOrDisableGasPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigEnableOrDisableGasPayload))
}

// CreateChainConfigOptimizeChargeGasPayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigOptimizeChargeGasPayload(enable bool) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigOptimizeChargeGasPayload", enable)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigOptimizeChargeGasPayload indicates an expected call of CreateChainConfigOptimizeChargeGasPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigOptimizeChargeGasPayload(enable interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigOptimizeChargeGasPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigOptimizeChargeGasPayload), enable)
}

// CreateChainConfigPermissionAddPayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigPermissionAddPayload(permissionResourceName string, policy *accesscontrol.Policy) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigPermissionAddPayload", permissionResourceName, policy)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigPermissionAddPayload indicates an expected call of CreateChainConfigPermissionAddPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigPermissionAddPayload(permissionResourceName, policy interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigPermissionAddPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigPermissionAddPayload), permissionResourceName, policy)
}

// CreateChainConfigPermissionDeletePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigPermissionDeletePayload(permissionResourceName string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigPermissionDeletePayload", permissionResourceName)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigPermissionDeletePayload indicates an expected call of CreateChainConfigPermissionDeletePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigPermissionDeletePayload(permissionResourceName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigPermissionDeletePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigPermissionDeletePayload), permissionResourceName)
}

// CreateChainConfigPermissionUpdatePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigPermissionUpdatePayload(permissionResourceName string, policy *accesscontrol.Policy) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigPermissionUpdatePayload", permissionResourceName, policy)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigPermissionUpdatePayload indicates an expected call of CreateChainConfigPermissionUpdatePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigPermissionUpdatePayload(permissionResourceName, policy interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigPermissionUpdatePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigPermissionUpdatePayload), permissionResourceName, policy)
}

// CreateChainConfigTrustMemberAddPayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigTrustMemberAddPayload(trustMemberOrgId, trustMemberNodeId, trustMemberRole, trustMemberInfo string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigTrustMemberAddPayload", trustMemberOrgId, trustMemberNodeId, trustMemberRole, trustMemberInfo)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigTrustMemberAddPayload indicates an expected call of CreateChainConfigTrustMemberAddPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigTrustMemberAddPayload(trustMemberOrgId, trustMemberNodeId, trustMemberRole, trustMemberInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigTrustMemberAddPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigTrustMemberAddPayload), trustMemberOrgId, trustMemberNodeId, trustMemberRole, trustMemberInfo)
}

// CreateChainConfigTrustMemberDeletePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigTrustMemberDeletePayload(trustMemberInfo string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigTrustMemberDeletePayload", trustMemberInfo)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigTrustMemberDeletePayload indicates an expected call of CreateChainConfigTrustMemberDeletePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigTrustMemberDeletePayload(trustMemberInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigTrustMemberDeletePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigTrustMemberDeletePayload), trustMemberInfo)
}

// CreateChainConfigTrustRootAddPayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigTrustRootAddPayload(trustRootOrgId string, trustRootCrt []string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigTrustRootAddPayload", trustRootOrgId, trustRootCrt)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigTrustRootAddPayload indicates an expected call of CreateChainConfigTrustRootAddPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigTrustRootAddPayload(trustRootOrgId, trustRootCrt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigTrustRootAddPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigTrustRootAddPayload), trustRootOrgId, trustRootCrt)
}

// CreateChainConfigTrustRootDeletePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigTrustRootDeletePayload(orgIdOrPKPubkeyPEM string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigTrustRootDeletePayload", orgIdOrPKPubkeyPEM)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigTrustRootDeletePayload indicates an expected call of CreateChainConfigTrustRootDeletePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigTrustRootDeletePayload(orgIdOrPKPubkeyPEM interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigTrustRootDeletePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigTrustRootDeletePayload), orgIdOrPKPubkeyPEM)
}

// CreateChainConfigTrustRootUpdatePayload mocks base method.
func (m *MockSDKInterface) CreateChainConfigTrustRootUpdatePayload(trustRootOrgId string, trustRootCrt []string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChainConfigTrustRootUpdatePayload", trustRootOrgId, trustRootCrt)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChainConfigTrustRootUpdatePayload indicates an expected call of CreateChainConfigTrustRootUpdatePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateChainConfigTrustRootUpdatePayload(trustRootOrgId, trustRootCrt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChainConfigTrustRootUpdatePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateChainConfigTrustRootUpdatePayload), trustRootOrgId, trustRootCrt)
}

// CreateContractCreatePayload mocks base method.
func (m *MockSDKInterface) CreateContractCreatePayload(contractName, version, byteCodeStringOrFilePath string, runtime common.RuntimeType, kvs []*common.KeyValuePair) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateContractCreatePayload", contractName, version, byteCodeStringOrFilePath, runtime, kvs)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateContractCreatePayload indicates an expected call of CreateContractCreatePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateContractCreatePayload(contractName, version, byteCodeStringOrFilePath, runtime, kvs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateContractCreatePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateContractCreatePayload), contractName, version, byteCodeStringOrFilePath, runtime, kvs)
}

// CreateContractFreezePayload mocks base method.
func (m *MockSDKInterface) CreateContractFreezePayload(contractName string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateContractFreezePayload", contractName)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateContractFreezePayload indicates an expected call of CreateContractFreezePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateContractFreezePayload(contractName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateContractFreezePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateContractFreezePayload), contractName)
}

// CreateContractRevokePayload mocks base method.
func (m *MockSDKInterface) CreateContractRevokePayload(contractName string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateContractRevokePayload", contractName)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateContractRevokePayload indicates an expected call of CreateContractRevokePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateContractRevokePayload(contractName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateContractRevokePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateContractRevokePayload), contractName)
}

// CreateContractUnfreezePayload mocks base method.
func (m *MockSDKInterface) CreateContractUnfreezePayload(contractName string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateContractUnfreezePayload", contractName)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateContractUnfreezePayload indicates an expected call of CreateContractUnfreezePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateContractUnfreezePayload(contractName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateContractUnfreezePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateContractUnfreezePayload), contractName)
}

// CreateContractUpgradePayload mocks base method.
func (m *MockSDKInterface) CreateContractUpgradePayload(contractName, version, byteCodeStringOrFilePath string, runtime common.RuntimeType, kvs []*common.KeyValuePair) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateContractUpgradePayload", contractName, version, byteCodeStringOrFilePath, runtime, kvs)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateContractUpgradePayload indicates an expected call of CreateContractUpgradePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateContractUpgradePayload(contractName, version, byteCodeStringOrFilePath, runtime, kvs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateContractUpgradePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateContractUpgradePayload), contractName, version, byteCodeStringOrFilePath, runtime, kvs)
}

// CreateDeleteCertsAliasPayload mocks base method.
func (m *MockSDKInterface) CreateDeleteCertsAliasPayload(aliases []string) *common.Payload {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDeleteCertsAliasPayload", aliases)
	ret0, _ := ret[0].(*common.Payload)
	return ret0
}

// CreateDeleteCertsAliasPayload indicates an expected call of CreateDeleteCertsAliasPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateDeleteCertsAliasPayload(aliases interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDeleteCertsAliasPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateDeleteCertsAliasPayload), aliases)
}

// CreateFrozenGasAccountPayload mocks base method.
func (m *MockSDKInterface) CreateFrozenGasAccountPayload(address string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateFrozenGasAccountPayload", address)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateFrozenGasAccountPayload indicates an expected call of CreateFrozenGasAccountPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateFrozenGasAccountPayload(address interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateFrozenGasAccountPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateFrozenGasAccountPayload), address)
}

// CreateHibeInitParamsTxPayloadParams mocks base method.
func (m *MockSDKInterface) CreateHibeInitParamsTxPayloadParams(orgId string, hibeParams []byte) ([]*common.KeyValuePair, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateHibeInitParamsTxPayloadParams", orgId, hibeParams)
	ret0, _ := ret[0].([]*common.KeyValuePair)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateHibeInitParamsTxPayloadParams indicates an expected call of CreateHibeInitParamsTxPayloadParams.
func (mr *MockSDKInterfaceMockRecorder) CreateHibeInitParamsTxPayloadParams(orgId, hibeParams interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHibeInitParamsTxPayloadParams", reflect.TypeOf((*MockSDKInterface)(nil).CreateHibeInitParamsTxPayloadParams), orgId, hibeParams)
}

// CreateHibeTxPayloadParamsWithHibeParams mocks base method.
func (m *MockSDKInterface) CreateHibeTxPayloadParamsWithHibeParams(plaintext []byte, receiverIds []string, paramsBytesList [][]byte, txId string, keyType crypto.KeyType) ([]*common.KeyValuePair, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateHibeTxPayloadParamsWithHibeParams", plaintext, receiverIds, paramsBytesList, txId, keyType)
	ret0, _ := ret[0].([]*common.KeyValuePair)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateHibeTxPayloadParamsWithHibeParams indicates an expected call of CreateHibeTxPayloadParamsWithHibeParams.
func (mr *MockSDKInterfaceMockRecorder) CreateHibeTxPayloadParamsWithHibeParams(plaintext, receiverIds, paramsBytesList, txId, keyType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHibeTxPayloadParamsWithHibeParams", reflect.TypeOf((*MockSDKInterface)(nil).CreateHibeTxPayloadParamsWithHibeParams), plaintext, receiverIds, paramsBytesList, txId, keyType)
}

// CreateHibeTxPayloadParamsWithoutHibeParams mocks base method.
func (m *MockSDKInterface) CreateHibeTxPayloadParamsWithoutHibeParams(contractName, queryParamsMethod string, plaintext []byte, receiverIds, receiverOrgIds []string, txId string, keyType crypto.KeyType, timeout int64) ([]*common.KeyValuePair, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateHibeTxPayloadParamsWithoutHibeParams", contractName, queryParamsMethod, plaintext, receiverIds, receiverOrgIds, txId, keyType, timeout)
	ret0, _ := ret[0].([]*common.KeyValuePair)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateHibeTxPayloadParamsWithoutHibeParams indicates an expected call of CreateHibeTxPayloadParamsWithoutHibeParams.
func (mr *MockSDKInterfaceMockRecorder) CreateHibeTxPayloadParamsWithoutHibeParams(contractName, queryParamsMethod, plaintext, receiverIds, receiverOrgIds, txId, keyType, timeout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHibeTxPayloadParamsWithoutHibeParams", reflect.TypeOf((*MockSDKInterface)(nil).CreateHibeTxPayloadParamsWithoutHibeParams), contractName, queryParamsMethod, plaintext, receiverIds, receiverOrgIds, txId, keyType, timeout)
}

// CreateMultiSignReqPayload mocks base method.
func (m *MockSDKInterface) CreateMultiSignReqPayload(pairs []*common.KeyValuePair) *common.Payload {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMultiSignReqPayload", pairs)
	ret0, _ := ret[0].(*common.Payload)
	return ret0
}

// CreateMultiSignReqPayload indicates an expected call of CreateMultiSignReqPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateMultiSignReqPayload(pairs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMultiSignReqPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateMultiSignReqPayload), pairs)
}

// CreateMultiSignReqPayloadWithGasLimit mocks base method.
func (m *MockSDKInterface) CreateMultiSignReqPayloadWithGasLimit(pairs []*common.KeyValuePair, gasLimit uint64) *common.Payload {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMultiSignReqPayloadWithGasLimit", pairs, gasLimit)
	ret0, _ := ret[0].(*common.Payload)
	return ret0
}

// CreateMultiSignReqPayloadWithGasLimit indicates an expected call of CreateMultiSignReqPayloadWithGasLimit.
func (mr *MockSDKInterfaceMockRecorder) CreateMultiSignReqPayloadWithGasLimit(pairs, gasLimit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMultiSignReqPayloadWithGasLimit", reflect.TypeOf((*MockSDKInterface)(nil).CreateMultiSignReqPayloadWithGasLimit), pairs, gasLimit)
}

// CreateNativeContractAccessGrantPayload mocks base method.
func (m *MockSDKInterface) CreateNativeContractAccessGrantPayload(grantContractList []string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNativeContractAccessGrantPayload", grantContractList)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNativeContractAccessGrantPayload indicates an expected call of CreateNativeContractAccessGrantPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateNativeContractAccessGrantPayload(grantContractList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNativeContractAccessGrantPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateNativeContractAccessGrantPayload), grantContractList)
}

// CreateNativeContractAccessRevokePayload mocks base method.
func (m *MockSDKInterface) CreateNativeContractAccessRevokePayload(revokeContractList []string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNativeContractAccessRevokePayload", revokeContractList)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNativeContractAccessRevokePayload indicates an expected call of CreateNativeContractAccessRevokePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateNativeContractAccessRevokePayload(revokeContractList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNativeContractAccessRevokePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateNativeContractAccessRevokePayload), revokeContractList)
}

// CreatePubkeyAddPayload mocks base method.
func (m *MockSDKInterface) CreatePubkeyAddPayload(pubkey, orgId, role string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePubkeyAddPayload", pubkey, orgId, role)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePubkeyAddPayload indicates an expected call of CreatePubkeyAddPayload.
func (mr *MockSDKInterfaceMockRecorder) CreatePubkeyAddPayload(pubkey, orgId, role interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePubkeyAddPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreatePubkeyAddPayload), pubkey, orgId, role)
}

// CreatePubkeyDelPayload mocks base method.
func (m *MockSDKInterface) CreatePubkeyDelPayload(pubkey, orgId string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePubkeyDelPayload", pubkey, orgId)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePubkeyDelPayload indicates an expected call of CreatePubkeyDelPayload.
func (mr *MockSDKInterfaceMockRecorder) CreatePubkeyDelPayload(pubkey, orgId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePubkeyDelPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreatePubkeyDelPayload), pubkey, orgId)
}

// CreatePubkeyQueryPayload mocks base method.
func (m *MockSDKInterface) CreatePubkeyQueryPayload(pubkey string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePubkeyQueryPayload", pubkey)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePubkeyQueryPayload indicates an expected call of CreatePubkeyQueryPayload.
func (mr *MockSDKInterfaceMockRecorder) CreatePubkeyQueryPayload(pubkey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePubkeyQueryPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreatePubkeyQueryPayload), pubkey)
}

// CreateRechargeGasPayload mocks base method.
func (m *MockSDKInterface) CreateRechargeGasPayload(rechargeGasList []*syscontract.RechargeGas) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRechargeGasPayload", rechargeGasList)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRechargeGasPayload indicates an expected call of CreateRechargeGasPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateRechargeGasPayload(rechargeGasList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRechargeGasPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateRechargeGasPayload), rechargeGasList)
}

// CreateRefundGasPayload mocks base method.
func (m *MockSDKInterface) CreateRefundGasPayload(address string, amount int64) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRefundGasPayload", address, amount)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRefundGasPayload indicates an expected call of CreateRefundGasPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateRefundGasPayload(address, amount interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRefundGasPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateRefundGasPayload), address, amount)
}

// CreateRestoreBlockPayload mocks base method.
func (m *MockSDKInterface) CreateRestoreBlockPayload(fullBlock []byte) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRestoreBlockPayload", fullBlock)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRestoreBlockPayload indicates an expected call of CreateRestoreBlockPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateRestoreBlockPayload(fullBlock interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRestoreBlockPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateRestoreBlockPayload), fullBlock)
}

// CreateSaveEnclaveCACertPayload mocks base method.
func (m *MockSDKInterface) CreateSaveEnclaveCACertPayload(caCert, txId string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSaveEnclaveCACertPayload", caCert, txId)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSaveEnclaveCACertPayload indicates an expected call of CreateSaveEnclaveCACertPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateSaveEnclaveCACertPayload(caCert, txId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSaveEnclaveCACertPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateSaveEnclaveCACertPayload), caCert, txId)
}

// CreateSaveEnclaveReportPayload mocks base method.
func (m *MockSDKInterface) CreateSaveEnclaveReportPayload(enclaveId, report, txId string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSaveEnclaveReportPayload", enclaveId, report, txId)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSaveEnclaveReportPayload indicates an expected call of CreateSaveEnclaveReportPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateSaveEnclaveReportPayload(enclaveId, report, txId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSaveEnclaveReportPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateSaveEnclaveReportPayload), enclaveId, report, txId)
}

// CreateSetGasAdminPayload mocks base method.
func (m *MockSDKInterface) CreateSetGasAdminPayload(address string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSetGasAdminPayload", address)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSetGasAdminPayload indicates an expected call of CreateSetGasAdminPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateSetGasAdminPayload(address interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSetGasAdminPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateSetGasAdminPayload), address)
}

// CreateSetInstallBaseGasPayload mocks base method.
func (m *MockSDKInterface) CreateSetInstallBaseGasPayload(installBaseGas int64) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSetInstallBaseGasPayload", installBaseGas)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSetInstallBaseGasPayload indicates an expected call of CreateSetInstallBaseGasPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateSetInstallBaseGasPayload(installBaseGas interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSetInstallBaseGasPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateSetInstallBaseGasPayload), installBaseGas)
}

// CreateSetInstallGasPricePayload mocks base method.
func (m *MockSDKInterface) CreateSetInstallGasPricePayload(installGasPrice string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSetInstallGasPricePayload", installGasPrice)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSetInstallGasPricePayload indicates an expected call of CreateSetInstallGasPricePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateSetInstallGasPricePayload(installGasPrice interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSetInstallGasPricePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateSetInstallGasPricePayload), installGasPrice)
}

// CreateSetInvokeBaseGasPayload mocks base method.
func (m *MockSDKInterface) CreateSetInvokeBaseGasPayload(invokeBaseGas int64) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSetInvokeBaseGasPayload", invokeBaseGas)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSetInvokeBaseGasPayload indicates an expected call of CreateSetInvokeBaseGasPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateSetInvokeBaseGasPayload(invokeBaseGas interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSetInvokeBaseGasPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateSetInvokeBaseGasPayload), invokeBaseGas)
}

// CreateSetInvokeGasPricePayload mocks base method.
func (m *MockSDKInterface) CreateSetInvokeGasPricePayload(invokeGasPrice string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSetInvokeGasPricePayload", invokeGasPrice)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSetInvokeGasPricePayload indicates an expected call of CreateSetInvokeGasPricePayload.
func (mr *MockSDKInterfaceMockRecorder) CreateSetInvokeGasPricePayload(invokeGasPrice interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSetInvokeGasPricePayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateSetInvokeGasPricePayload), invokeGasPrice)
}

// CreateUnfrozenGasAccountPayload mocks base method.
func (m *MockSDKInterface) CreateUnfrozenGasAccountPayload(address string) (*common.Payload, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUnfrozenGasAccountPayload", address)
	ret0, _ := ret[0].(*common.Payload)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateUnfrozenGasAccountPayload indicates an expected call of CreateUnfrozenGasAccountPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateUnfrozenGasAccountPayload(address interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUnfrozenGasAccountPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateUnfrozenGasAccountPayload), address)
}

// CreateUpdateCertByAliasPayload mocks base method.
func (m *MockSDKInterface) CreateUpdateCertByAliasPayload(alias, newCertPEM string) *common.Payload {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUpdateCertByAliasPayload", alias, newCertPEM)
	ret0, _ := ret[0].(*common.Payload)
	return ret0
}

// CreateUpdateCertByAliasPayload indicates an expected call of CreateUpdateCertByAliasPayload.
func (mr *MockSDKInterfaceMockRecorder) CreateUpdateCertByAliasPayload(alias, newCertPEM interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUpdateCertByAliasPayload", reflect.TypeOf((*MockSDKInterface)(nil).CreateUpdateCertByAliasPayload), alias, newCertPEM)
}

// DecryptHibeTxByTxId mocks base method.
func (m *MockSDKInterface) DecryptHibeTxByTxId(localId string, hibeParams, hibePrvKey []byte, txId string, keyType crypto.KeyType) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecryptHibeTxByTxId", localId, hibeParams, hibePrvKey, txId, keyType)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DecryptHibeTxByTxId indicates an expected call of DecryptHibeTxByTxId.
func (mr *MockSDKInterfaceMockRecorder) DecryptHibeTxByTxId(localId, hibeParams, hibePrvKey, txId, keyType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecryptHibeTxByTxId", reflect.TypeOf((*MockSDKInterface)(nil).DecryptHibeTxByTxId), localId, hibeParams, hibePrvKey, txId, keyType)
}

// DeleteCert mocks base method.
func (m *MockSDKInterface) DeleteCert(certHashes []string) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCert", certHashes)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCert indicates an expected call of DeleteCert.
func (mr *MockSDKInterfaceMockRecorder) DeleteCert(certHashes interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCert", reflect.TypeOf((*MockSDKInterface)(nil).DeleteCert), certHashes)
}

// DeleteCertsAlias mocks base method.
func (m *MockSDKInterface) DeleteCertsAlias(payload *common.Payload, endorsers []*common.EndorsementEntry, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCertsAlias", payload, endorsers, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCertsAlias indicates an expected call of DeleteCertsAlias.
func (mr *MockSDKInterfaceMockRecorder) DeleteCertsAlias(payload, endorsers, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCertsAlias", reflect.TypeOf((*MockSDKInterface)(nil).DeleteCertsAlias), payload, endorsers, timeout, withSyncResult)
}

// DisableCertHash mocks base method.
func (m *MockSDKInterface) DisableCertHash() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisableCertHash")
	ret0, _ := ret[0].(error)
	return ret0
}

// DisableCertHash indicates an expected call of DisableCertHash.
func (mr *MockSDKInterfaceMockRecorder) DisableCertHash() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableCertHash", reflect.TypeOf((*MockSDKInterface)(nil).DisableCertHash))
}

// EnableCertHash mocks base method.
func (m *MockSDKInterface) EnableCertHash() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableCertHash")
	ret0, _ := ret[0].(error)
	return ret0
}

// EnableCertHash indicates an expected call of EnableCertHash.
func (mr *MockSDKInterfaceMockRecorder) EnableCertHash() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableCertHash", reflect.TypeOf((*MockSDKInterface)(nil).EnableCertHash))
}

// EstimateGas mocks base method.
func (m *MockSDKInterface) EstimateGas(payload *common.Payload) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EstimateGas", payload)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EstimateGas indicates an expected call of EstimateGas.
func (mr *MockSDKInterfaceMockRecorder) EstimateGas(payload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EstimateGas", reflect.TypeOf((*MockSDKInterface)(nil).EstimateGas), payload)
}

// GetArchiveStatus mocks base method.
func (m *MockSDKInterface) GetArchiveStatus() (*store.ArchiveStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetArchiveStatus")
	ret0, _ := ret[0].(*store.ArchiveStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetArchiveStatus indicates an expected call of GetArchiveStatus.
func (mr *MockSDKInterfaceMockRecorder) GetArchiveStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetArchiveStatus", reflect.TypeOf((*MockSDKInterface)(nil).GetArchiveStatus))
}

// GetArchivedBlockByHash mocks base method.
func (m *MockSDKInterface) GetArchivedBlockByHash(blockHash string, withRWSet bool) (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetArchivedBlockByHash", blockHash, withRWSet)
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetArchivedBlockByHash indicates an expected call of GetArchivedBlockByHash.
func (mr *MockSDKInterfaceMockRecorder) GetArchivedBlockByHash(blockHash, withRWSet interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetArchivedBlockByHash", reflect.TypeOf((*MockSDKInterface)(nil).GetArchivedBlockByHash), blockHash, withRWSet)
}

// GetArchivedBlockByHeight mocks base method.
func (m *MockSDKInterface) GetArchivedBlockByHeight(blockHeight uint64, withRWSet bool) (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetArchivedBlockByHeight", blockHeight, withRWSet)
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetArchivedBlockByHeight indicates an expected call of GetArchivedBlockByHeight.
func (mr *MockSDKInterfaceMockRecorder) GetArchivedBlockByHeight(blockHeight, withRWSet interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetArchivedBlockByHeight", reflect.TypeOf((*MockSDKInterface)(nil).GetArchivedBlockByHeight), blockHeight, withRWSet)
}

// GetArchivedBlockByTxId mocks base method.
func (m *MockSDKInterface) GetArchivedBlockByTxId(txId string, withRWSet bool) (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetArchivedBlockByTxId", txId, withRWSet)
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetArchivedBlockByTxId indicates an expected call of GetArchivedBlockByTxId.
func (mr *MockSDKInterfaceMockRecorder) GetArchivedBlockByTxId(txId, withRWSet interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetArchivedBlockByTxId", reflect.TypeOf((*MockSDKInterface)(nil).GetArchivedBlockByTxId), txId, withRWSet)
}

// GetArchivedBlockHeight mocks base method.
func (m *MockSDKInterface) GetArchivedBlockHeight() (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetArchivedBlockHeight")
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetArchivedBlockHeight indicates an expected call of GetArchivedBlockHeight.
func (mr *MockSDKInterfaceMockRecorder) GetArchivedBlockHeight() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetArchivedBlockHeight", reflect.TypeOf((*MockSDKInterface)(nil).GetArchivedBlockHeight))
}

// GetArchivedTxByTxId mocks base method.
func (m *MockSDKInterface) GetArchivedTxByTxId(txId string) (*common.TransactionInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetArchivedTxByTxId", txId)
	ret0, _ := ret[0].(*common.TransactionInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetArchivedTxByTxId indicates an expected call of GetArchivedTxByTxId.
func (mr *MockSDKInterfaceMockRecorder) GetArchivedTxByTxId(txId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetArchivedTxByTxId", reflect.TypeOf((*MockSDKInterface)(nil).GetArchivedTxByTxId), txId)
}

// GetBlockByHash mocks base method.
func (m *MockSDKInterface) GetBlockByHash(blockHash string, withRWSet bool) (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockByHash", blockHash, withRWSet)
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockByHash indicates an expected call of GetBlockByHash.
func (mr *MockSDKInterfaceMockRecorder) GetBlockByHash(blockHash, withRWSet interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockByHash", reflect.TypeOf((*MockSDKInterface)(nil).GetBlockByHash), blockHash, withRWSet)
}

// GetBlockByHeight mocks base method.
func (m *MockSDKInterface) GetBlockByHeight(blockHeight uint64, withRWSet bool) (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockByHeight", blockHeight, withRWSet)
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockByHeight indicates an expected call of GetBlockByHeight.
func (mr *MockSDKInterfaceMockRecorder) GetBlockByHeight(blockHeight, withRWSet interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockByHeight", reflect.TypeOf((*MockSDKInterface)(nil).GetBlockByHeight), blockHeight, withRWSet)
}

// GetBlockByHeightTruncate mocks base method.
func (m *MockSDKInterface) GetBlockByHeightTruncate(blockHeight uint64, withRWSet bool, truncateLength int, truncateModel string) (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockByHeightTruncate", blockHeight, withRWSet, truncateLength, truncateModel)
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockByHeightTruncate indicates an expected call of GetBlockByHeightTruncate.
func (mr *MockSDKInterfaceMockRecorder) GetBlockByHeightTruncate(blockHeight, withRWSet, truncateLength, truncateModel interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockByHeightTruncate", reflect.TypeOf((*MockSDKInterface)(nil).GetBlockByHeightTruncate), blockHeight, withRWSet, truncateLength, truncateModel)
}

// GetBlockByTxId mocks base method.
func (m *MockSDKInterface) GetBlockByTxId(txId string, withRWSet bool) (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockByTxId", txId, withRWSet)
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockByTxId indicates an expected call of GetBlockByTxId.
func (mr *MockSDKInterfaceMockRecorder) GetBlockByTxId(txId, withRWSet interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockByTxId", reflect.TypeOf((*MockSDKInterface)(nil).GetBlockByTxId), txId, withRWSet)
}

// GetBlockHeaderByHeight mocks base method.
func (m *MockSDKInterface) GetBlockHeaderByHeight(blockHeight uint64) (*common.BlockHeader, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockHeaderByHeight", blockHeight)
	ret0, _ := ret[0].(*common.BlockHeader)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockHeaderByHeight indicates an expected call of GetBlockHeaderByHeight.
func (mr *MockSDKInterfaceMockRecorder) GetBlockHeaderByHeight(blockHeight interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockHeaderByHeight", reflect.TypeOf((*MockSDKInterface)(nil).GetBlockHeaderByHeight), blockHeight)
}

// GetBlockHeightByHash mocks base method.
func (m *MockSDKInterface) GetBlockHeightByHash(blockHash string) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockHeightByHash", blockHash)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockHeightByHash indicates an expected call of GetBlockHeightByHash.
func (mr *MockSDKInterfaceMockRecorder) GetBlockHeightByHash(blockHash interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockHeightByHash", reflect.TypeOf((*MockSDKInterface)(nil).GetBlockHeightByHash), blockHash)
}

// GetBlockHeightByTxId mocks base method.
func (m *MockSDKInterface) GetBlockHeightByTxId(txId string) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockHeightByTxId", txId)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockHeightByTxId indicates an expected call of GetBlockHeightByTxId.
func (mr *MockSDKInterfaceMockRecorder) GetBlockHeightByTxId(txId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockHeightByTxId", reflect.TypeOf((*MockSDKInterface)(nil).GetBlockHeightByTxId), txId)
}

// GetCertHash mocks base method.
func (m *MockSDKInterface) GetCertHash() ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCertHash")
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCertHash indicates an expected call of GetCertHash.
func (mr *MockSDKInterfaceMockRecorder) GetCertHash() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCertHash", reflect.TypeOf((*MockSDKInterface)(nil).GetCertHash))
}

// GetChainConfig mocks base method.
func (m *MockSDKInterface) GetChainConfig() (*config.ChainConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChainConfig")
	ret0, _ := ret[0].(*config.ChainConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChainConfig indicates an expected call of GetChainConfig.
func (mr *MockSDKInterfaceMockRecorder) GetChainConfig() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChainConfig", reflect.TypeOf((*MockSDKInterface)(nil).GetChainConfig))
}

// GetChainConfigByBlockHeight mocks base method.
func (m *MockSDKInterface) GetChainConfigByBlockHeight(blockHeight uint64) (*config.ChainConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChainConfigByBlockHeight", blockHeight)
	ret0, _ := ret[0].(*config.ChainConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChainConfigByBlockHeight indicates an expected call of GetChainConfigByBlockHeight.
func (mr *MockSDKInterfaceMockRecorder) GetChainConfigByBlockHeight(blockHeight interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChainConfigByBlockHeight", reflect.TypeOf((*MockSDKInterface)(nil).GetChainConfigByBlockHeight), blockHeight)
}

// GetChainConfigPermissionList mocks base method.
func (m *MockSDKInterface) GetChainConfigPermissionList() ([]*config.ResourcePolicy, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChainConfigPermissionList")
	ret0, _ := ret[0].([]*config.ResourcePolicy)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChainConfigPermissionList indicates an expected call of GetChainConfigPermissionList.
func (mr *MockSDKInterfaceMockRecorder) GetChainConfigPermissionList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChainConfigPermissionList", reflect.TypeOf((*MockSDKInterface)(nil).GetChainConfigPermissionList))
}

// GetChainConfigSequence mocks base method.
func (m *MockSDKInterface) GetChainConfigSequence() (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChainConfigSequence")
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChainConfigSequence indicates an expected call of GetChainConfigSequence.
func (mr *MockSDKInterfaceMockRecorder) GetChainConfigSequence() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChainConfigSequence", reflect.TypeOf((*MockSDKInterface)(nil).GetChainConfigSequence))
}

// GetChainInfo mocks base method.
func (m *MockSDKInterface) GetChainInfo() (*discovery.ChainInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChainInfo")
	ret0, _ := ret[0].(*discovery.ChainInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChainInfo indicates an expected call of GetChainInfo.
func (mr *MockSDKInterfaceMockRecorder) GetChainInfo() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChainInfo", reflect.TypeOf((*MockSDKInterface)(nil).GetChainInfo))
}

// GetChainMakerServerVersion mocks base method.
func (m *MockSDKInterface) GetChainMakerServerVersion() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChainMakerServerVersion")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChainMakerServerVersion indicates an expected call of GetChainMakerServerVersion.
func (mr *MockSDKInterfaceMockRecorder) GetChainMakerServerVersion() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChainMakerServerVersion", reflect.TypeOf((*MockSDKInterface)(nil).GetChainMakerServerVersion))
}

// GetChainMakerServerVersionCustom mocks base method.
func (m *MockSDKInterface) GetChainMakerServerVersionCustom(ctx context.Context) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChainMakerServerVersionCustom", ctx)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChainMakerServerVersionCustom indicates an expected call of GetChainMakerServerVersionCustom.
func (mr *MockSDKInterfaceMockRecorder) GetChainMakerServerVersionCustom(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChainMakerServerVersionCustom", reflect.TypeOf((*MockSDKInterface)(nil).GetChainMakerServerVersionCustom), ctx)
}

// GetContract mocks base method.
func (m *MockSDKInterface) GetContract(contractName, codeHash string) (*common.PrivateGetContract, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContract", contractName, codeHash)
	ret0, _ := ret[0].(*common.PrivateGetContract)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContract indicates an expected call of GetContract.
func (mr *MockSDKInterfaceMockRecorder) GetContract(contractName, codeHash interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContract", reflect.TypeOf((*MockSDKInterface)(nil).GetContract), contractName, codeHash)
}

// GetContractInfo mocks base method.
func (m *MockSDKInterface) GetContractInfo(contractName string) (*common.Contract, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContractInfo", contractName)
	ret0, _ := ret[0].(*common.Contract)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContractInfo indicates an expected call of GetContractInfo.
func (mr *MockSDKInterfaceMockRecorder) GetContractInfo(contractName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContractInfo", reflect.TypeOf((*MockSDKInterface)(nil).GetContractInfo), contractName)
}

// GetContractList mocks base method.
func (m *MockSDKInterface) GetContractList() ([]*common.Contract, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContractList")
	ret0, _ := ret[0].([]*common.Contract)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContractList indicates an expected call of GetContractList.
func (mr *MockSDKInterfaceMockRecorder) GetContractList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContractList", reflect.TypeOf((*MockSDKInterface)(nil).GetContractList))
}

// GetCurrentBlockHeight mocks base method.
func (m *MockSDKInterface) GetCurrentBlockHeight() (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentBlockHeight")
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentBlockHeight indicates an expected call of GetCurrentBlockHeight.
func (mr *MockSDKInterfaceMockRecorder) GetCurrentBlockHeight() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentBlockHeight", reflect.TypeOf((*MockSDKInterface)(nil).GetCurrentBlockHeight))
}

// GetData mocks base method.
func (m *MockSDKInterface) GetData(contractName, key string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", contractName, key)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockSDKInterfaceMockRecorder) GetData(contractName, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockSDKInterface)(nil).GetData), contractName, key)
}

// GetDir mocks base method.
func (m *MockSDKInterface) GetDir(orderId string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDir", orderId)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDir indicates an expected call of GetDir.
func (mr *MockSDKInterfaceMockRecorder) GetDir(orderId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDir", reflect.TypeOf((*MockSDKInterface)(nil).GetDir), orderId)
}

// GetDisabledNativeContractList mocks base method.
func (m *MockSDKInterface) GetDisabledNativeContractList() ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDisabledNativeContractList")
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDisabledNativeContractList indicates an expected call of GetDisabledNativeContractList.
func (mr *MockSDKInterfaceMockRecorder) GetDisabledNativeContractList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDisabledNativeContractList", reflect.TypeOf((*MockSDKInterface)(nil).GetDisabledNativeContractList))
}

// GetEnclaveCACert mocks base method.
func (m *MockSDKInterface) GetEnclaveCACert() ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnclaveCACert")
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnclaveCACert indicates an expected call of GetEnclaveCACert.
func (mr *MockSDKInterfaceMockRecorder) GetEnclaveCACert() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnclaveCACert", reflect.TypeOf((*MockSDKInterface)(nil).GetEnclaveCACert))
}

// GetEnclaveChallenge mocks base method.
func (m *MockSDKInterface) GetEnclaveChallenge(enclaveId string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnclaveChallenge", enclaveId)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnclaveChallenge indicates an expected call of GetEnclaveChallenge.
func (mr *MockSDKInterfaceMockRecorder) GetEnclaveChallenge(enclaveId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnclaveChallenge", reflect.TypeOf((*MockSDKInterface)(nil).GetEnclaveChallenge), enclaveId)
}

// GetEnclaveEncryptPubKey mocks base method.
func (m *MockSDKInterface) GetEnclaveEncryptPubKey(enclaveId string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnclaveEncryptPubKey", enclaveId)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnclaveEncryptPubKey indicates an expected call of GetEnclaveEncryptPubKey.
func (mr *MockSDKInterfaceMockRecorder) GetEnclaveEncryptPubKey(enclaveId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnclaveEncryptPubKey", reflect.TypeOf((*MockSDKInterface)(nil).GetEnclaveEncryptPubKey), enclaveId)
}

// GetEnclaveProof mocks base method.
func (m *MockSDKInterface) GetEnclaveProof(enclaveId string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnclaveProof", enclaveId)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnclaveProof indicates an expected call of GetEnclaveProof.
func (mr *MockSDKInterfaceMockRecorder) GetEnclaveProof(enclaveId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnclaveProof", reflect.TypeOf((*MockSDKInterface)(nil).GetEnclaveProof), enclaveId)
}

// GetEnclaveReport mocks base method.
func (m *MockSDKInterface) GetEnclaveReport(enclaveId string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnclaveReport", enclaveId)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnclaveReport indicates an expected call of GetEnclaveReport.
func (mr *MockSDKInterfaceMockRecorder) GetEnclaveReport(enclaveId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnclaveReport", reflect.TypeOf((*MockSDKInterface)(nil).GetEnclaveReport), enclaveId)
}

// GetEnclaveSignature mocks base method.
func (m *MockSDKInterface) GetEnclaveSignature(enclaveId string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnclaveSignature", enclaveId)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnclaveSignature indicates an expected call of GetEnclaveSignature.
func (mr *MockSDKInterfaceMockRecorder) GetEnclaveSignature(enclaveId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnclaveSignature", reflect.TypeOf((*MockSDKInterface)(nil).GetEnclaveSignature), enclaveId)
}

// GetEnclaveVerificationPubKey mocks base method.
func (m *MockSDKInterface) GetEnclaveVerificationPubKey(enclaveId string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnclaveVerificationPubKey", enclaveId)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnclaveVerificationPubKey indicates an expected call of GetEnclaveVerificationPubKey.
func (mr *MockSDKInterfaceMockRecorder) GetEnclaveVerificationPubKey(enclaveId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnclaveVerificationPubKey", reflect.TypeOf((*MockSDKInterface)(nil).GetEnclaveVerificationPubKey), enclaveId)
}

// GetFullBlockByHeight mocks base method.
func (m *MockSDKInterface) GetFullBlockByHeight(blockHeight uint64) (*store.BlockWithRWSet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFullBlockByHeight", blockHeight)
	ret0, _ := ret[0].(*store.BlockWithRWSet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFullBlockByHeight indicates an expected call of GetFullBlockByHeight.
func (mr *MockSDKInterfaceMockRecorder) GetFullBlockByHeight(blockHeight interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFullBlockByHeight", reflect.TypeOf((*MockSDKInterface)(nil).GetFullBlockByHeight), blockHeight)
}

// GetGasAccountStatus mocks base method.
func (m *MockSDKInterface) GetGasAccountStatus(address string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasAccountStatus", address)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasAccountStatus indicates an expected call of GetGasAccountStatus.
func (mr *MockSDKInterfaceMockRecorder) GetGasAccountStatus(address interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasAccountStatus", reflect.TypeOf((*MockSDKInterface)(nil).GetGasAccountStatus), address)
}

// GetGasAdmin mocks base method.
func (m *MockSDKInterface) GetGasAdmin() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasAdmin")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasAdmin indicates an expected call of GetGasAdmin.
func (mr *MockSDKInterfaceMockRecorder) GetGasAdmin() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasAdmin", reflect.TypeOf((*MockSDKInterface)(nil).GetGasAdmin))
}

// GetGasBalance mocks base method.
func (m *MockSDKInterface) GetGasBalance(address string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasBalance", address)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasBalance indicates an expected call of GetGasBalance.
func (mr *MockSDKInterfaceMockRecorder) GetGasBalance(address interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasBalance", reflect.TypeOf((*MockSDKInterface)(nil).GetGasBalance), address)
}

// GetLastBlock mocks base method.
func (m *MockSDKInterface) GetLastBlock(withRWSet bool) (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastBlock", withRWSet)
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastBlock indicates an expected call of GetLastBlock.
func (mr *MockSDKInterfaceMockRecorder) GetLastBlock(withRWSet interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastBlock", reflect.TypeOf((*MockSDKInterface)(nil).GetLastBlock), withRWSet)
}

// GetLastConfigBlock mocks base method.
func (m *MockSDKInterface) GetLastConfigBlock(withRWSet bool) (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastConfigBlock", withRWSet)
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastConfigBlock indicates an expected call of GetLastConfigBlock.
func (mr *MockSDKInterfaceMockRecorder) GetLastConfigBlock(withRWSet interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastConfigBlock", reflect.TypeOf((*MockSDKInterface)(nil).GetLastConfigBlock), withRWSet)
}

// GetMerklePathByTxId mocks base method.
func (m *MockSDKInterface) GetMerklePathByTxId(txId string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMerklePathByTxId", txId)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMerklePathByTxId indicates an expected call of GetMerklePathByTxId.
func (mr *MockSDKInterfaceMockRecorder) GetMerklePathByTxId(txId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMerklePathByTxId", reflect.TypeOf((*MockSDKInterface)(nil).GetMerklePathByTxId), txId)
}

// GetNodeChainList mocks base method.
func (m *MockSDKInterface) GetNodeChainList() (*discovery.ChainList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeChainList")
	ret0, _ := ret[0].(*discovery.ChainList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeChainList indicates an expected call of GetNodeChainList.
func (mr *MockSDKInterfaceMockRecorder) GetNodeChainList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeChainList", reflect.TypeOf((*MockSDKInterface)(nil).GetNodeChainList))
}

// GetPoolStatus mocks base method.
func (m *MockSDKInterface) GetPoolStatus() (*txpool.TxPoolStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPoolStatus")
	ret0, _ := ret[0].(*txpool.TxPoolStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPoolStatus indicates an expected call of GetPoolStatus.
func (mr *MockSDKInterfaceMockRecorder) GetPoolStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPoolStatus", reflect.TypeOf((*MockSDKInterface)(nil).GetPoolStatus))
}

// GetTxByTxId mocks base method.
func (m *MockSDKInterface) GetTxByTxId(txId string) (*common.TransactionInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTxByTxId", txId)
	ret0, _ := ret[0].(*common.TransactionInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTxByTxId indicates an expected call of GetTxByTxId.
func (mr *MockSDKInterfaceMockRecorder) GetTxByTxId(txId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTxByTxId", reflect.TypeOf((*MockSDKInterface)(nil).GetTxByTxId), txId)
}

// GetTxIdsByTypeAndStage mocks base method.
func (m *MockSDKInterface) GetTxIdsByTypeAndStage(txType txpool.TxType, txStage txpool.TxStage) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTxIdsByTypeAndStage", txType, txStage)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTxIdsByTypeAndStage indicates an expected call of GetTxIdsByTypeAndStage.
func (mr *MockSDKInterfaceMockRecorder) GetTxIdsByTypeAndStage(txType, txStage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTxIdsByTypeAndStage", reflect.TypeOf((*MockSDKInterface)(nil).GetTxIdsByTypeAndStage), txType, txStage)
}

// GetTxRequest mocks base method.
func (m *MockSDKInterface) GetTxRequest(contractName, method, txId string, kvs []*common.KeyValuePair) (*common.TxRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTxRequest", contractName, method, txId, kvs)
	ret0, _ := ret[0].(*common.TxRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTxRequest indicates an expected call of GetTxRequest.
func (mr *MockSDKInterfaceMockRecorder) GetTxRequest(contractName, method, txId, kvs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTxRequest", reflect.TypeOf((*MockSDKInterface)(nil).GetTxRequest), contractName, method, txId, kvs)
}

// GetTxWithRWSetByTxId mocks base method.
func (m *MockSDKInterface) GetTxWithRWSetByTxId(txId string) (*common.TransactionInfoWithRWSet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTxWithRWSetByTxId", txId)
	ret0, _ := ret[0].(*common.TransactionInfoWithRWSet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTxWithRWSetByTxId indicates an expected call of GetTxWithRWSetByTxId.
func (mr *MockSDKInterfaceMockRecorder) GetTxWithRWSetByTxId(txId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTxWithRWSetByTxId", reflect.TypeOf((*MockSDKInterface)(nil).GetTxWithRWSetByTxId), txId)
}

// GetTxsInPoolByTxIds mocks base method.
func (m *MockSDKInterface) GetTxsInPoolByTxIds(txIds []string) ([]*common.Transaction, []string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTxsInPoolByTxIds", txIds)
	ret0, _ := ret[0].([]*common.Transaction)
	ret1, _ := ret[1].([]string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetTxsInPoolByTxIds indicates an expected call of GetTxsInPoolByTxIds.
func (mr *MockSDKInterfaceMockRecorder) GetTxsInPoolByTxIds(txIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTxsInPoolByTxIds", reflect.TypeOf((*MockSDKInterface)(nil).GetTxsInPoolByTxIds), txIds)
}

// InvokeContract mocks base method.
func (m *MockSDKInterface) InvokeContract(contractName, method, txId string, kvs []*common.KeyValuePair, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InvokeContract", contractName, method, txId, kvs, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InvokeContract indicates an expected call of InvokeContract.
func (mr *MockSDKInterfaceMockRecorder) InvokeContract(contractName, method, txId, kvs, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvokeContract", reflect.TypeOf((*MockSDKInterface)(nil).InvokeContract), contractName, method, txId, kvs, timeout, withSyncResult)
}

// InvokeContractWithLimit mocks base method.
func (m *MockSDKInterface) InvokeContractWithLimit(contractName, method, txId string, kvs []*common.KeyValuePair, timeout int64, withSyncResult bool, limit *common.Limit) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InvokeContractWithLimit", contractName, method, txId, kvs, timeout, withSyncResult, limit)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InvokeContractWithLimit indicates an expected call of InvokeContractWithLimit.
func (mr *MockSDKInterfaceMockRecorder) InvokeContractWithLimit(contractName, method, txId, kvs, timeout, withSyncResult, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvokeContractWithLimit", reflect.TypeOf((*MockSDKInterface)(nil).InvokeContractWithLimit), contractName, method, txId, kvs, timeout, withSyncResult, limit)
}

// InvokeSystemContract mocks base method.
func (m *MockSDKInterface) InvokeSystemContract(contractName, method, txId string, kvs []*common.KeyValuePair, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InvokeSystemContract", contractName, method, txId, kvs, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InvokeSystemContract indicates an expected call of InvokeSystemContract.
func (mr *MockSDKInterfaceMockRecorder) InvokeSystemContract(contractName, method, txId, kvs, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InvokeSystemContract", reflect.TypeOf((*MockSDKInterface)(nil).InvokeSystemContract), contractName, method, txId, kvs, timeout, withSyncResult)
}

// MultiSignContractQuery mocks base method.
func (m *MockSDKInterface) MultiSignContractQuery(txId string) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultiSignContractQuery", txId)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiSignContractQuery indicates an expected call of MultiSignContractQuery.
func (mr *MockSDKInterfaceMockRecorder) MultiSignContractQuery(txId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiSignContractQuery", reflect.TypeOf((*MockSDKInterface)(nil).MultiSignContractQuery), txId)
}

// MultiSignContractQueryWithParams mocks base method.
func (m *MockSDKInterface) MultiSignContractQueryWithParams(txId string, params []*common.KeyValuePair) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultiSignContractQueryWithParams", txId, params)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiSignContractQueryWithParams indicates an expected call of MultiSignContractQueryWithParams.
func (mr *MockSDKInterfaceMockRecorder) MultiSignContractQueryWithParams(txId, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiSignContractQueryWithParams", reflect.TypeOf((*MockSDKInterface)(nil).MultiSignContractQueryWithParams), txId, params)
}

// MultiSignContractReq mocks base method.
func (m *MockSDKInterface) MultiSignContractReq(payload *common.Payload, endorsers []*common.EndorsementEntry, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultiSignContractReq", payload, endorsers, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiSignContractReq indicates an expected call of MultiSignContractReq.
func (mr *MockSDKInterfaceMockRecorder) MultiSignContractReq(payload, endorsers, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiSignContractReq", reflect.TypeOf((*MockSDKInterface)(nil).MultiSignContractReq), payload, endorsers, timeout, withSyncResult)
}

// MultiSignContractReqWithPayer mocks base method.
func (m *MockSDKInterface) MultiSignContractReqWithPayer(payload *common.Payload, endorsers []*common.EndorsementEntry, payer *common.EndorsementEntry, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultiSignContractReqWithPayer", payload, endorsers, payer, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiSignContractReqWithPayer indicates an expected call of MultiSignContractReqWithPayer.
func (mr *MockSDKInterfaceMockRecorder) MultiSignContractReqWithPayer(payload, endorsers, payer, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiSignContractReqWithPayer", reflect.TypeOf((*MockSDKInterface)(nil).MultiSignContractReqWithPayer), payload, endorsers, payer, timeout, withSyncResult)
}

// MultiSignContractTrig mocks base method.
func (m *MockSDKInterface) MultiSignContractTrig(multiSignReqPayload *common.Payload, timeout int64, limit *common.Limit, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultiSignContractTrig", multiSignReqPayload, timeout, limit, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiSignContractTrig indicates an expected call of MultiSignContractTrig.
func (mr *MockSDKInterfaceMockRecorder) MultiSignContractTrig(multiSignReqPayload, timeout, limit, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiSignContractTrig", reflect.TypeOf((*MockSDKInterface)(nil).MultiSignContractTrig), multiSignReqPayload, timeout, limit, withSyncResult)
}

// MultiSignContractTrigWithPayer mocks base method.
func (m *MockSDKInterface) MultiSignContractTrigWithPayer(multiSignReqPayload *common.Payload, payer *common.EndorsementEntry, timeout int64, limit *common.Limit, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultiSignContractTrigWithPayer", multiSignReqPayload, payer, timeout, limit, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiSignContractTrigWithPayer indicates an expected call of MultiSignContractTrigWithPayer.
func (mr *MockSDKInterfaceMockRecorder) MultiSignContractTrigWithPayer(multiSignReqPayload, payer, timeout, limit, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiSignContractTrigWithPayer", reflect.TypeOf((*MockSDKInterface)(nil).MultiSignContractTrigWithPayer), multiSignReqPayload, payer, timeout, limit, withSyncResult)
}

// MultiSignContractVote mocks base method.
func (m *MockSDKInterface) MultiSignContractVote(payload *common.Payload, endorser *common.EndorsementEntry, isAgree bool, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultiSignContractVote", payload, endorser, isAgree, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiSignContractVote indicates an expected call of MultiSignContractVote.
func (mr *MockSDKInterfaceMockRecorder) MultiSignContractVote(payload, endorser, isAgree, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiSignContractVote", reflect.TypeOf((*MockSDKInterface)(nil).MultiSignContractVote), payload, endorser, isAgree, timeout, withSyncResult)
}

// MultiSignContractVoteWithGasLimit mocks base method.
func (m *MockSDKInterface) MultiSignContractVoteWithGasLimit(payload *common.Payload, endorser *common.EndorsementEntry, isAgree bool, timeout int64, gasLimit uint64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultiSignContractVoteWithGasLimit", payload, endorser, isAgree, timeout, gasLimit, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiSignContractVoteWithGasLimit indicates an expected call of MultiSignContractVoteWithGasLimit.
func (mr *MockSDKInterfaceMockRecorder) MultiSignContractVoteWithGasLimit(payload, endorser, isAgree, timeout, gasLimit, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiSignContractVoteWithGasLimit", reflect.TypeOf((*MockSDKInterface)(nil).MultiSignContractVoteWithGasLimit), payload, endorser, isAgree, timeout, gasLimit, withSyncResult)
}

// MultiSignContractVoteWithGasLimitAndPayer mocks base method.
func (m *MockSDKInterface) MultiSignContractVoteWithGasLimitAndPayer(payload *common.Payload, endorser, payer *common.EndorsementEntry, isAgree bool, timeout int64, gasLimit uint64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MultiSignContractVoteWithGasLimitAndPayer", payload, endorser, payer, isAgree, timeout, gasLimit, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MultiSignContractVoteWithGasLimitAndPayer indicates an expected call of MultiSignContractVoteWithGasLimitAndPayer.
func (mr *MockSDKInterfaceMockRecorder) MultiSignContractVoteWithGasLimitAndPayer(payload, endorser, payer, isAgree, timeout, gasLimit, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MultiSignContractVoteWithGasLimitAndPayer", reflect.TypeOf((*MockSDKInterface)(nil).MultiSignContractVoteWithGasLimitAndPayer), payload, endorser, payer, isAgree, timeout, gasLimit, withSyncResult)
}

// QueryCert mocks base method.
func (m *MockSDKInterface) QueryCert(certHashes []string) (*common.CertInfos, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryCert", certHashes)
	ret0, _ := ret[0].(*common.CertInfos)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryCert indicates an expected call of QueryCert.
func (mr *MockSDKInterfaceMockRecorder) QueryCert(certHashes interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryCert", reflect.TypeOf((*MockSDKInterface)(nil).QueryCert), certHashes)
}

// QueryCertsAlias mocks base method.
func (m *MockSDKInterface) QueryCertsAlias(aliases []string) (*common.AliasInfos, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryCertsAlias", aliases)
	ret0, _ := ret[0].(*common.AliasInfos)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryCertsAlias indicates an expected call of QueryCertsAlias.
func (mr *MockSDKInterfaceMockRecorder) QueryCertsAlias(aliases interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryCertsAlias", reflect.TypeOf((*MockSDKInterface)(nil).QueryCertsAlias), aliases)
}

// QueryContract mocks base method.
func (m *MockSDKInterface) QueryContract(contractName, method string, kvs []*common.KeyValuePair, timeout int64) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryContract", contractName, method, kvs, timeout)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryContract indicates an expected call of QueryContract.
func (mr *MockSDKInterfaceMockRecorder) QueryContract(contractName, method, kvs, timeout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryContract", reflect.TypeOf((*MockSDKInterface)(nil).QueryContract), contractName, method, kvs, timeout)
}

// QueryHibeParamsWithOrgId mocks base method.
func (m *MockSDKInterface) QueryHibeParamsWithOrgId(contractName, method, orgId string, timeout int64) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryHibeParamsWithOrgId", contractName, method, orgId, timeout)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryHibeParamsWithOrgId indicates an expected call of QueryHibeParamsWithOrgId.
func (mr *MockSDKInterfaceMockRecorder) QueryHibeParamsWithOrgId(contractName, method, orgId, timeout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryHibeParamsWithOrgId", reflect.TypeOf((*MockSDKInterface)(nil).QueryHibeParamsWithOrgId), contractName, method, orgId, timeout)
}

// QuerySystemContract mocks base method.
func (m *MockSDKInterface) QuerySystemContract(contractName, method string, kvs []*common.KeyValuePair, timeout int64) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QuerySystemContract", contractName, method, kvs, timeout)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QuerySystemContract indicates an expected call of QuerySystemContract.
func (mr *MockSDKInterfaceMockRecorder) QuerySystemContract(contractName, method, kvs, timeout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QuerySystemContract", reflect.TypeOf((*MockSDKInterface)(nil).QuerySystemContract), contractName, method, kvs, timeout)
}

// RestoreBlocks mocks base method.
func (m *MockSDKInterface) RestoreBlocks(restoreHeight uint64, mode string, heightNoticeCallback func(chainmaker_sdk_go.ProcessMessage) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestoreBlocks", restoreHeight, mode, heightNoticeCallback)
	ret0, _ := ret[0].(error)
	return ret0
}

// RestoreBlocks indicates an expected call of RestoreBlocks.
func (mr *MockSDKInterfaceMockRecorder) RestoreBlocks(restoreHeight, mode, heightNoticeCallback interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestoreBlocks", reflect.TypeOf((*MockSDKInterface)(nil).RestoreBlocks), restoreHeight, mode, heightNoticeCallback)
}

// SaveData mocks base method.
func (m *MockSDKInterface) SaveData(contractName, contractVersion string, isDeployment bool, codeHash, reportHash []byte, result *common.ContractResult, codeHeader []byte, txId string, rwSet *common.TxRWSet, sign []byte, events *common.StrSlice, privateReq []byte, withSyncResult bool, timeout int64) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveData", contractName, contractVersion, isDeployment, codeHash, reportHash, result, codeHeader, txId, rwSet, sign, events, privateReq, withSyncResult, timeout)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveData indicates an expected call of SaveData.
func (mr *MockSDKInterfaceMockRecorder) SaveData(contractName, contractVersion, isDeployment, codeHash, reportHash, result, codeHeader, txId, rwSet, sign, events, privateReq, withSyncResult, timeout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveData", reflect.TypeOf((*MockSDKInterface)(nil).SaveData), contractName, contractVersion, isDeployment, codeHash, reportHash, result, codeHeader, txId, rwSet, sign, events, privateReq, withSyncResult, timeout)
}

// SaveDir mocks base method.
func (m *MockSDKInterface) SaveDir(orderId, txId string, privateDir *common.StrSlice, withSyncResult bool, timeout int64) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveDir", orderId, txId, privateDir, withSyncResult, timeout)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveDir indicates an expected call of SaveDir.
func (mr *MockSDKInterfaceMockRecorder) SaveDir(orderId, txId, privateDir, withSyncResult, timeout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveDir", reflect.TypeOf((*MockSDKInterface)(nil).SaveDir), orderId, txId, privateDir, withSyncResult, timeout)
}

// SaveRemoteAttestationProof mocks base method.
func (m *MockSDKInterface) SaveRemoteAttestationProof(proof, txId string, withSyncResult bool, timeout int64) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveRemoteAttestationProof", proof, txId, withSyncResult, timeout)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveRemoteAttestationProof indicates an expected call of SaveRemoteAttestationProof.
func (mr *MockSDKInterfaceMockRecorder) SaveRemoteAttestationProof(proof, txId, withSyncResult, timeout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveRemoteAttestationProof", reflect.TypeOf((*MockSDKInterface)(nil).SaveRemoteAttestationProof), proof, txId, withSyncResult, timeout)
}

// SendArchiveBlockRequest mocks base method.
func (m *MockSDKInterface) SendArchiveBlockRequest(payload *common.Payload, timeout int64) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendArchiveBlockRequest", payload, timeout)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendArchiveBlockRequest indicates an expected call of SendArchiveBlockRequest.
func (mr *MockSDKInterfaceMockRecorder) SendArchiveBlockRequest(payload, timeout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendArchiveBlockRequest", reflect.TypeOf((*MockSDKInterface)(nil).SendArchiveBlockRequest), payload, timeout)
}

// SendCertManageRequest mocks base method.
func (m *MockSDKInterface) SendCertManageRequest(payload *common.Payload, endorsers []*common.EndorsementEntry, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendCertManageRequest", payload, endorsers, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendCertManageRequest indicates an expected call of SendCertManageRequest.
func (mr *MockSDKInterfaceMockRecorder) SendCertManageRequest(payload, endorsers, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCertManageRequest", reflect.TypeOf((*MockSDKInterface)(nil).SendCertManageRequest), payload, endorsers, timeout, withSyncResult)
}

// SendChainConfigUpdateRequest mocks base method.
func (m *MockSDKInterface) SendChainConfigUpdateRequest(payload *common.Payload, endorsers []*common.EndorsementEntry, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendChainConfigUpdateRequest", payload, endorsers, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendChainConfigUpdateRequest indicates an expected call of SendChainConfigUpdateRequest.
func (mr *MockSDKInterfaceMockRecorder) SendChainConfigUpdateRequest(payload, endorsers, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendChainConfigUpdateRequest", reflect.TypeOf((*MockSDKInterface)(nil).SendChainConfigUpdateRequest), payload, endorsers, timeout, withSyncResult)
}

// SendContractManageRequest mocks base method.
func (m *MockSDKInterface) SendContractManageRequest(payload *common.Payload, endorsers []*common.EndorsementEntry, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendContractManageRequest", payload, endorsers, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendContractManageRequest indicates an expected call of SendContractManageRequest.
func (mr *MockSDKInterfaceMockRecorder) SendContractManageRequest(payload, endorsers, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendContractManageRequest", reflect.TypeOf((*MockSDKInterface)(nil).SendContractManageRequest), payload, endorsers, timeout, withSyncResult)
}

// SendContractManageRequestWithPayer mocks base method.
func (m *MockSDKInterface) SendContractManageRequestWithPayer(payload *common.Payload, endorsers []*common.EndorsementEntry, payer *common.EndorsementEntry, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendContractManageRequestWithPayer", payload, endorsers, payer, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendContractManageRequestWithPayer indicates an expected call of SendContractManageRequestWithPayer.
func (mr *MockSDKInterfaceMockRecorder) SendContractManageRequestWithPayer(payload, endorsers, payer, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendContractManageRequestWithPayer", reflect.TypeOf((*MockSDKInterface)(nil).SendContractManageRequestWithPayer), payload, endorsers, payer, timeout, withSyncResult)
}

// SendGasManageRequest mocks base method.
func (m *MockSDKInterface) SendGasManageRequest(payload *common.Payload, endorsers []*common.EndorsementEntry, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendGasManageRequest", payload, endorsers, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendGasManageRequest indicates an expected call of SendGasManageRequest.
func (mr *MockSDKInterfaceMockRecorder) SendGasManageRequest(payload, endorsers, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendGasManageRequest", reflect.TypeOf((*MockSDKInterface)(nil).SendGasManageRequest), payload, endorsers, timeout, withSyncResult)
}

// SendPubkeyManageRequest mocks base method.
func (m *MockSDKInterface) SendPubkeyManageRequest(payload *common.Payload, endorsers []*common.EndorsementEntry, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPubkeyManageRequest", payload, endorsers, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPubkeyManageRequest indicates an expected call of SendPubkeyManageRequest.
func (mr *MockSDKInterfaceMockRecorder) SendPubkeyManageRequest(payload, endorsers, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPubkeyManageRequest", reflect.TypeOf((*MockSDKInterface)(nil).SendPubkeyManageRequest), payload, endorsers, timeout, withSyncResult)
}

// SendRestoreBlockRequest mocks base method.
func (m *MockSDKInterface) SendRestoreBlockRequest(payload *common.Payload, timeout int64) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendRestoreBlockRequest", payload, timeout)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendRestoreBlockRequest indicates an expected call of SendRestoreBlockRequest.
func (mr *MockSDKInterfaceMockRecorder) SendRestoreBlockRequest(payload, timeout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRestoreBlockRequest", reflect.TypeOf((*MockSDKInterface)(nil).SendRestoreBlockRequest), payload, timeout)
}

// SendTxRequest mocks base method.
func (m *MockSDKInterface) SendTxRequest(txRequest *common.TxRequest, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendTxRequest", txRequest, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendTxRequest indicates an expected call of SendTxRequest.
func (mr *MockSDKInterfaceMockRecorder) SendTxRequest(txRequest, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendTxRequest", reflect.TypeOf((*MockSDKInterface)(nil).SendTxRequest), txRequest, timeout, withSyncResult)
}

// SignCertManagePayload mocks base method.
func (m *MockSDKInterface) SignCertManagePayload(payload *common.Payload) (*common.EndorsementEntry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SignCertManagePayload", payload)
	ret0, _ := ret[0].(*common.EndorsementEntry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SignCertManagePayload indicates an expected call of SignCertManagePayload.
func (mr *MockSDKInterfaceMockRecorder) SignCertManagePayload(payload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignCertManagePayload", reflect.TypeOf((*MockSDKInterface)(nil).SignCertManagePayload), payload)
}

// SignChainConfigPayload mocks base method.
func (m *MockSDKInterface) SignChainConfigPayload(payload *common.Payload) (*common.EndorsementEntry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SignChainConfigPayload", payload)
	ret0, _ := ret[0].(*common.EndorsementEntry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SignChainConfigPayload indicates an expected call of SignChainConfigPayload.
func (mr *MockSDKInterfaceMockRecorder) SignChainConfigPayload(payload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignChainConfigPayload", reflect.TypeOf((*MockSDKInterface)(nil).SignChainConfigPayload), payload)
}

// SignContractManagePayload mocks base method.
func (m *MockSDKInterface) SignContractManagePayload(payload *common.Payload) (*common.EndorsementEntry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SignContractManagePayload", payload)
	ret0, _ := ret[0].(*common.EndorsementEntry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SignContractManagePayload indicates an expected call of SignContractManagePayload.
func (mr *MockSDKInterfaceMockRecorder) SignContractManagePayload(payload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignContractManagePayload", reflect.TypeOf((*MockSDKInterface)(nil).SignContractManagePayload), payload)
}

// SignDeleteAliasPayload mocks base method.
func (m *MockSDKInterface) SignDeleteAliasPayload(payload *common.Payload) (*common.EndorsementEntry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SignDeleteAliasPayload", payload)
	ret0, _ := ret[0].(*common.EndorsementEntry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SignDeleteAliasPayload indicates an expected call of SignDeleteAliasPayload.
func (mr *MockSDKInterfaceMockRecorder) SignDeleteAliasPayload(payload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignDeleteAliasPayload", reflect.TypeOf((*MockSDKInterface)(nil).SignDeleteAliasPayload), payload)
}

// SignUpdateCertByAliasPayload mocks base method.
func (m *MockSDKInterface) SignUpdateCertByAliasPayload(payload *common.Payload) (*common.EndorsementEntry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SignUpdateCertByAliasPayload", payload)
	ret0, _ := ret[0].(*common.EndorsementEntry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SignUpdateCertByAliasPayload indicates an expected call of SignUpdateCertByAliasPayload.
func (mr *MockSDKInterfaceMockRecorder) SignUpdateCertByAliasPayload(payload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignUpdateCertByAliasPayload", reflect.TypeOf((*MockSDKInterface)(nil).SignUpdateCertByAliasPayload), payload)
}

// Stop mocks base method.
func (m *MockSDKInterface) Stop() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stop")
	ret0, _ := ret[0].(error)
	return ret0
}

// Stop indicates an expected call of Stop.
func (mr *MockSDKInterfaceMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockSDKInterface)(nil).Stop))
}

// Subscribe mocks base method.
func (m *MockSDKInterface) Subscribe(ctx context.Context, payloadBytes *common.Payload) (<-chan interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Subscribe", ctx, payloadBytes)
	ret0, _ := ret[0].(<-chan interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Subscribe indicates an expected call of Subscribe.
func (mr *MockSDKInterfaceMockRecorder) Subscribe(ctx, payloadBytes interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Subscribe", reflect.TypeOf((*MockSDKInterface)(nil).Subscribe), ctx, payloadBytes)
}

// SubscribeBlock mocks base method.
func (m *MockSDKInterface) SubscribeBlock(ctx context.Context, startBlock, endBlock int64, withRWSet, onlyHeader bool) (<-chan interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubscribeBlock", ctx, startBlock, endBlock, withRWSet, onlyHeader)
	ret0, _ := ret[0].(<-chan interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubscribeBlock indicates an expected call of SubscribeBlock.
func (mr *MockSDKInterfaceMockRecorder) SubscribeBlock(ctx, startBlock, endBlock, withRWSet, onlyHeader interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeBlock", reflect.TypeOf((*MockSDKInterface)(nil).SubscribeBlock), ctx, startBlock, endBlock, withRWSet, onlyHeader)
}

// SubscribeContractEvent mocks base method.
func (m *MockSDKInterface) SubscribeContractEvent(ctx context.Context, startBlock, endBlock int64, contractName, topic string) (<-chan interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubscribeContractEvent", ctx, startBlock, endBlock, contractName, topic)
	ret0, _ := ret[0].(<-chan interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubscribeContractEvent indicates an expected call of SubscribeContractEvent.
func (mr *MockSDKInterfaceMockRecorder) SubscribeContractEvent(ctx, startBlock, endBlock, contractName, topic interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeContractEvent", reflect.TypeOf((*MockSDKInterface)(nil).SubscribeContractEvent), ctx, startBlock, endBlock, contractName, topic)
}

// SubscribeTx mocks base method.
func (m *MockSDKInterface) SubscribeTx(ctx context.Context, startBlock, endBlock int64, contractName string, txIds []string) (<-chan interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubscribeTx", ctx, startBlock, endBlock, contractName, txIds)
	ret0, _ := ret[0].(<-chan interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubscribeTx indicates an expected call of SubscribeTx.
func (mr *MockSDKInterfaceMockRecorder) SubscribeTx(ctx, startBlock, endBlock, contractName, txIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubscribeTx", reflect.TypeOf((*MockSDKInterface)(nil).SubscribeTx), ctx, startBlock, endBlock, contractName, txIds)
}

// UpdateCertByAlias mocks base method.
func (m *MockSDKInterface) UpdateCertByAlias(payload *common.Payload, endorsers []*common.EndorsementEntry, timeout int64, withSyncResult bool) (*common.TxResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCertByAlias", payload, endorsers, timeout, withSyncResult)
	ret0, _ := ret[0].(*common.TxResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCertByAlias indicates an expected call of UpdateCertByAlias.
func (mr *MockSDKInterfaceMockRecorder) UpdateCertByAlias(payload, endorsers, timeout, withSyncResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCertByAlias", reflect.TypeOf((*MockSDKInterface)(nil).UpdateCertByAlias), payload, endorsers, timeout, withSyncResult)
}

// MockArchiveService is a mock of ArchiveService interface.
type MockArchiveService struct {
	ctrl     *gomock.Controller
	recorder *MockArchiveServiceMockRecorder
}

// MockArchiveServiceMockRecorder is the mock recorder for MockArchiveService.
type MockArchiveServiceMockRecorder struct {
	mock *MockArchiveService
}

// NewMockArchiveService creates a new mock instance.
func NewMockArchiveService(ctrl *gomock.Controller) *MockArchiveService {
	mock := &MockArchiveService{ctrl: ctrl}
	mock.recorder = &MockArchiveServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockArchiveService) EXPECT() *MockArchiveServiceMockRecorder {
	return m.recorder
}

// ArchiveBlock mocks base method.
func (m *MockArchiveService) ArchiveBlock(block *common.BlockInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ArchiveBlock", block)
	ret0, _ := ret[0].(error)
	return ret0
}

// ArchiveBlock indicates an expected call of ArchiveBlock.
func (mr *MockArchiveServiceMockRecorder) ArchiveBlock(block interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ArchiveBlock", reflect.TypeOf((*MockArchiveService)(nil).ArchiveBlock), block)
}

// ArchiveBlocks mocks base method.
func (m *MockArchiveService) ArchiveBlocks(bi chainmaker_sdk_go.BlockIterator, heightNoticeCallback func(chainmaker_sdk_go.ProcessMessage) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ArchiveBlocks", bi, heightNoticeCallback)
	ret0, _ := ret[0].(error)
	return ret0
}

// ArchiveBlocks indicates an expected call of ArchiveBlocks.
func (mr *MockArchiveServiceMockRecorder) ArchiveBlocks(bi, heightNoticeCallback interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ArchiveBlocks", reflect.TypeOf((*MockArchiveService)(nil).ArchiveBlocks), bi, heightNoticeCallback)
}

// Close mocks base method.
func (m *MockArchiveService) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockArchiveServiceMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockArchiveService)(nil).Close))
}

// GetArchivedStatus mocks base method.
func (m *MockArchiveService) GetArchivedStatus() (uint64, bool, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetArchivedStatus")
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(uint32)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetArchivedStatus indicates an expected call of GetArchivedStatus.
func (mr *MockArchiveServiceMockRecorder) GetArchivedStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetArchivedStatus", reflect.TypeOf((*MockArchiveService)(nil).GetArchivedStatus))
}

// GetBlockByHash mocks base method.
func (m *MockArchiveService) GetBlockByHash(blockHash string, withRWSet bool) (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockByHash", blockHash, withRWSet)
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockByHash indicates an expected call of GetBlockByHash.
func (mr *MockArchiveServiceMockRecorder) GetBlockByHash(blockHash, withRWSet interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockByHash", reflect.TypeOf((*MockArchiveService)(nil).GetBlockByHash), blockHash, withRWSet)
}

// GetBlockByHeight mocks base method.
func (m *MockArchiveService) GetBlockByHeight(blockHeight uint64, withRWSet bool) (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockByHeight", blockHeight, withRWSet)
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockByHeight indicates an expected call of GetBlockByHeight.
func (mr *MockArchiveServiceMockRecorder) GetBlockByHeight(blockHeight, withRWSet interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockByHeight", reflect.TypeOf((*MockArchiveService)(nil).GetBlockByHeight), blockHeight, withRWSet)
}

// GetBlockByTxId mocks base method.
func (m *MockArchiveService) GetBlockByTxId(txId string, withRWSet bool) (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockByTxId", txId, withRWSet)
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockByTxId indicates an expected call of GetBlockByTxId.
func (mr *MockArchiveServiceMockRecorder) GetBlockByTxId(txId, withRWSet interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockByTxId", reflect.TypeOf((*MockArchiveService)(nil).GetBlockByTxId), txId, withRWSet)
}

// GetChainConfigByBlockHeight mocks base method.
func (m *MockArchiveService) GetChainConfigByBlockHeight(blockHeight uint64) (*config.ChainConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChainConfigByBlockHeight", blockHeight)
	ret0, _ := ret[0].(*config.ChainConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChainConfigByBlockHeight indicates an expected call of GetChainConfigByBlockHeight.
func (mr *MockArchiveServiceMockRecorder) GetChainConfigByBlockHeight(blockHeight interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChainConfigByBlockHeight", reflect.TypeOf((*MockArchiveService)(nil).GetChainConfigByBlockHeight), blockHeight)
}

// GetTxByTxId mocks base method.
func (m *MockArchiveService) GetTxByTxId(txId string) (*common.TransactionInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTxByTxId", txId)
	ret0, _ := ret[0].(*common.TransactionInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTxByTxId indicates an expected call of GetTxByTxId.
func (mr *MockArchiveServiceMockRecorder) GetTxByTxId(txId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTxByTxId", reflect.TypeOf((*MockArchiveService)(nil).GetTxByTxId), txId)
}

// GetTxWithRWSetByTxId mocks base method.
func (m *MockArchiveService) GetTxWithRWSetByTxId(txId string) (*common.TransactionInfoWithRWSet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTxWithRWSetByTxId", txId)
	ret0, _ := ret[0].(*common.TransactionInfoWithRWSet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTxWithRWSetByTxId indicates an expected call of GetTxWithRWSetByTxId.
func (mr *MockArchiveServiceMockRecorder) GetTxWithRWSetByTxId(txId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTxWithRWSetByTxId", reflect.TypeOf((*MockArchiveService)(nil).GetTxWithRWSetByTxId), txId)
}

// Register mocks base method.
func (m *MockArchiveService) Register(genesis *common.BlockInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Register", genesis)
	ret0, _ := ret[0].(error)
	return ret0
}

// Register indicates an expected call of Register.
func (mr *MockArchiveServiceMockRecorder) Register(genesis interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Register", reflect.TypeOf((*MockArchiveService)(nil).Register), genesis)
}

// MockBlockIterator is a mock of BlockIterator interface.
type MockBlockIterator struct {
	ctrl     *gomock.Controller
	recorder *MockBlockIteratorMockRecorder
}

// MockBlockIteratorMockRecorder is the mock recorder for MockBlockIterator.
type MockBlockIteratorMockRecorder struct {
	mock *MockBlockIterator
}

// NewMockBlockIterator creates a new mock instance.
func NewMockBlockIterator(ctrl *gomock.Controller) *MockBlockIterator {
	mock := &MockBlockIterator{ctrl: ctrl}
	mock.recorder = &MockBlockIteratorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBlockIterator) EXPECT() *MockBlockIteratorMockRecorder {
	return m.recorder
}

// Current mocks base method.
func (m *MockBlockIterator) Current() uint64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Current")
	ret0, _ := ret[0].(uint64)
	return ret0
}

// Current indicates an expected call of Current.
func (mr *MockBlockIteratorMockRecorder) Current() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Current", reflect.TypeOf((*MockBlockIterator)(nil).Current))
}

// Next mocks base method.
func (m *MockBlockIterator) Next() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Next")
	ret0, _ := ret[0].(bool)
	return ret0
}

// Next indicates an expected call of Next.
func (mr *MockBlockIteratorMockRecorder) Next() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Next", reflect.TypeOf((*MockBlockIterator)(nil).Next))
}

// Release mocks base method.
func (m *MockBlockIterator) Release() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Release")
}

// Release indicates an expected call of Release.
func (mr *MockBlockIteratorMockRecorder) Release() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Release", reflect.TypeOf((*MockBlockIterator)(nil).Release))
}

// Total mocks base method.
func (m *MockBlockIterator) Total() uint64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Total")
	ret0, _ := ret[0].(uint64)
	return ret0
}

// Total indicates an expected call of Total.
func (mr *MockBlockIteratorMockRecorder) Total() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Total", reflect.TypeOf((*MockBlockIterator)(nil).Total))
}

// Value mocks base method.
func (m *MockBlockIterator) Value() (*common.BlockInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Value")
	ret0, _ := ret[0].(*common.BlockInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Value indicates an expected call of Value.
func (mr *MockBlockIteratorMockRecorder) Value() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Value", reflect.TypeOf((*MockBlockIterator)(nil).Value))
}
