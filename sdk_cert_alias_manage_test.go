/*
Copyright (C) THL A29 Limited, a Tencent company. All rights reserved.

SPDX-License-Identifier: Apache-2.0
*/

package chainmaker_sdk_go

import (
	"testing"
)

func TestAddAlias(t *testing.T) {}

func TestQueryCurrentAlias(t *testing.T) {}

func TestQueryCertByAliasAndBlockHeight(t *testing.T) {}

func TestCreateUpdateAliasPayload(t *testing.T) {}

func TestSignUpdateAliasPayload(t *testing.T) {}

func TestUpdateAlias(t *testing.T) {}
