/*
Copyright (C) THL A29 Limited, a Tencent company. All rights reserved.

SPDX-License-Identifier: Apache-2.0
*/

package chainmaker_sdk_go

import (
	"sort"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHandleResultArray(t *testing.T) {
	hrs := make([]*handleResult, 0)
	hrs = append(hrs, newHandleResult("node1", 0, 10))
	hrs = append(hrs, newHandleResult("node2", 4, 100))
	hrs = append(hrs, newHandleResult("node3", 2, 100))
	hrs = append(hrs, newHandleResult("node4", 3, 300))
	hrs = append(hrs, newHandleResult("node5", 2, 99))
	sort.Sort(handleResultArray(hrs))
	assert.Equal(t, "node2", hrs[0].nodeAddr)
	assert.Equal(t, "node4", hrs[1].nodeAddr)
	assert.Equal(t, "node5", hrs[2].nodeAddr)
	assert.Equal(t, "node3", hrs[3].nodeAddr)
	assert.Equal(t, "node1", hrs[4].nodeAddr)
}
