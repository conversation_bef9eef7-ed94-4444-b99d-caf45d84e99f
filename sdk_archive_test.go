/*
Copyright (C) THL A29 Limited, a Tencent company. All rights reserved.

SPDX-License-Identifier: Apache-2.0
*/

package chainmaker_sdk_go

import (
	"testing"

	"github.com/stretchr/testify/require"

	"chainmaker.org/chainmaker/pb-go/v2/common"
)

func TestSendArchiveBlockRequest(t *testing.T) {
	tests := []struct {
		name         string
		height       uint64
		serverTxResp *common.TxResponse
		serverErr    error
	}{
		{
			"good",
			10,
			&common.TxResponse{Code: common.TxStatusCode_SUCCESS},
			nil,
		},
		{
			"block already archived",
			100,
			&common.TxResponse{Code: common.TxStatusCode_ARCHIVED_BLOCK},
			nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cli, err := newMockChainClient(tt.serverTxResp, tt.serverErr, WithConfPath(sdkConfigPathForUT))
			require.Nil(t, err)
			defer cli.Stop()

			var (
				payload *common.Payload
				resp    *common.TxResponse
			)

			payload, err = cli.CreateArchiveBlockPayload(tt.height)
			require.Nil(t, err)

			resp, err = cli.SendArchiveBlockRequest(payload, -1)
			require.Equal(t, tt.serverErr, err)
			require.Equal(t, tt.serverTxResp.Code, resp.Code)
		})
	}
}

func TestSendRestoreBlockRequest(t *testing.T) {
	tests := []struct {
		name         string
		fullblock    []byte
		serverTxResp *common.TxResponse
		serverErr    error
	}{
		{
			"bad",
			[]byte("fullblock"),
			&common.TxResponse{Code: common.TxStatusCode_CONTRACT_FAIL},
			nil,
		},
		{
			"good",
			[]byte("\n¬P\n \n\b\u0014\u001A\u0006chain1 \u0001* ¡/rs\u0004\u001Dà\u0091$«\u0005Ì,\u0092\u0092O\v­_îºâà#M\u00069Èo \u0017a: \u000Eç\u009B\u0010ÕË\u0000\u0006ì\u0016:\u009Fgy\u0087\u001B\u0097z\u008Chn[Ï\u008DãkAhî\u009FY\u0012H\u0001R n:¢æ\u008BÌ°Êk\u008B ÖH§ð§ôô\u0092-é9²hÒãO\u0004\u000F>\u00913Z \bÚ|EË Cwçä\"IÍ¥q?¨e\u0011mÛ´ËZ\u0019I²å´8¦«b \u008B#¤\u0004Ã\u0002Ú\t\u0093= \u000Fï°ª~máØ²ð\u0002\u001CDÜ©\u001Cè\u009Eò\u001E\u001Bh\u008F\u0092Î\u0088\u0006z\u0094\b\n\u0016wx-org3.chainmaker.org\u001Aù\a-----BEGIN CERTIFICATE-----\nMIICwjCCAmigAwIBAgIDC+3AMAoGCCqGSM49BAMCMIGKMQswCQYDVQQGEwJDTjEQ\nMA4GA1UECBMHQmVpamluZzEQMA4GA1UEBxMHQmVpamluZzEfMB0GA1UEChMWd3gt\nb3JnMy5jaGFpbm1ha2VyLm9yZzESMBAGA1UECxMJcm9vdC1jZXJ0MSIwIAYDVQQD\nExljYS53eC1vcmczLmNoYWlubWFrZXIub3JnMB4XDTIxMDgxMTA4MDMxN1oXDTI2\nMDgxMDA4MDMxN1owgZcxCzAJBgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlqaW5nMRAw\nDgYDVQQHEwdCZWlqaW5nMR8wHQYDVQQKExZ3eC1vcmczLmNoYWlubWFrZXIub3Jn\nMRIwEAYDVQQLEwljb25zZW5zdXMxLzAtBgNVBAMTJmNvbnNlbnN1czEuc2lnbi53\neC1vcmczLmNoYWlubWFrZXIub3JnMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE\nIm5jTPjo3UqmAmlsCGgiYZF4uMDzUGUyaiDrkgsQNfMHj1l+JpbJpBvXFopM9Lg9\nEJSQBbSygNXPQESNuPkM86OBrTCBqjAOBgNVHQ8BAf8EBAMCAaYwDwYDVR0lBAgw\nBgYEVR0lADApBgNVHQ4EIgQgFSdB45UdpkbH8OdxG7QNSLVRm7ETxjaf1F+NymWs\nCZ8wKwYDVR0jBCQwIoAgWFLM1GDIAklQLXYGJBPOUqMRepJOIY5XCb85E57wxjMw\nLwYLgSdYj2QLHo9kCwQEIDg1YTc0NjdkZjQxZjQzMDJhN2IzZTYwZDAxNTYwYTdl\nMAoGCCqGSM49BAMCA0gAMEUCIQCtdZoWkUiCaEm5NRz4/rP+FUkpOLv2gJ0sv0Ro\nAd8lsQIgeEoGsU0aESP79JuhYpRrHJJi+bKWbRX6BUeOfOfgaks=\n-----END CERTIFICATE-----\n\u0082\u0001H0F\u0002!\u0000È³8\u0091G\u0099Ð`\u009B\u0012N\u000E\v\u009DÞÒÞõT\u0083\u0015½ö]È\u001B¿\v]èÚô\u0002!\u0000\u00801ì\u009C§/0ØcË\u0012\u0093>\u001F/À\u001D³\u0013C\u001Fñ¡\u0002\u0010£\u009FÜ|\bôM\u0012\u0002\u0012\u0000\u001Aë\t\ng\n\u0006chain1\u001A@98d32153edcb4e7393044c8c9ecb5bd1d50534a23c1247f783d79727c2eaad2e \u008E\u0092Î\u0088\u00062\vCERT_MANAGE:\bCERT_ADD\u0012\u0093\b\nÇ\a\n\u0016wx-org1.chainmaker.org\u001A¬\a-----BEGIN CERTIFICATE-----\nMIICijCCAi+gAwIBAgIDDXisMAoGCCqGSM49BAMCMIGKMQswCQYDVQQGEwJDTjEQ\nMA4GA1UECBMHQmVpamluZzEQMA4GA1UEBxMHQmVpamluZzEfMB0GA1UEChMWd3gt\nb3JnMS5jaGFpbm1ha2VyLm9yZzESMBAGA1UECxMJcm9vdC1jZXJ0MSIwIAYDVQQD\nExljYS53eC1vcmcxLmNoYWlubWFrZXIub3JnMB4XDTIxMDgxMTA4MDMxN1oXDTI2\nMDgxMDA4MDMxN1owgZExCzAJBgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlqaW5nMRAw\nDgYDVQQHEwdCZWlqaW5nMR8wHQYDVQQKExZ3eC1vcmcxLmNoYWlubWFrZXIub3Jn\nMQ8wDQYDVQQLEwZjbGllbnQxLDAqBgNVBAMTI2NsaWVudDEuc2lnbi53eC1vcmcx\nLmNoYWlubWFrZXIub3JnMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEQBZcw4AT\nFTGl16tFYzZ2cuemLVABI61aS3CeHbUoaAarumE57M/Yv2POtjiNhlk4GS8YZUHU\nKob7G2DP1wI056N7MHkwDgYDVR0PAQH/BAQDAgGmMA8GA1UdJQQIMAYGBFUdJQAw\nKQYDVR0OBCIEINrcPwy5PcHjLsfvdeukukpo1gvqOV8Bxj8Ll/oRlmGaMCsGA1Ud\nIwQkMCKAINS+0LGXWV9F/uPmIexhJtVlrrBWJ6dGqmdNS3bzMtSkMAoGCCqGSM49\nBAMCA0kAMEYCIQDgp1sdA+1pQy6w3qQPHumSuIB4+BormFZu3IiYCOBWYQIhAKb/\nkiWekJhlhUVOgglNWlYlp3A8+8fFv0UKl39xRMi8\n-----END CERTIFICATE-----\n\u0012G0E\u0002 %§+·£\u0012cÚÅ8P%IZa%\u0011-ÊW\a¨\u008A×¸{Ñ¸[@1¨\u0002!\u0000\u0089\"f¶z\u001A÷\u0081#\"ÂãùåÛ\u0087\u0091®Ö¾bH¡aO÷\u009E¢\u0010õÏ¾\"j\u0012F\u0012@fd7ce8e8f61ae8f831fd8144ac5b98aeacd84100671d1293af70f3464772b3f3\u001A\u0002OK\u001A \u008B#¤\u0004Ã\u0002Ú\t\u0093= \u000Fï°ª~máØ²ð\u0002\u001CDÜ©\u001Cè\u009Eò\u001E\u001B\"\u0094<\n\u0091<\n\u0014TBFTAddtionalDataKey\u0012ø;\b\u0001\u0010\u0001 \u0003* ¡/rs\u0004\u001Dà\u0091$«\u0005Ì,\u0092\u0092O\v­_îºâà#M\u00069Èo \u0017a2ì\t\n.QmWAmZZmy3o4XnmvbaAyc41W3We44zqhkh2xJQaSJb8TfT\u0012¹\t\b\u0001\u0012.QmWAmZZmy3o4XnmvbaAyc41W3We44zqhkh2xJQaSJb8TfT\u0018\u0001* ¡/rs\u0004\u001Dà\u0091$«\u0005Ì,\u0092\u0092O\v­_îºâà#M\u00069Èo \u0017a2à\b\n\u0094\b\n\u0016wx-org3.chainmaker.org\u001Aù\a-----BEGIN CERTIFICATE-----\nMIICwjCCAmigAwIBAgIDC+3AMAoGCCqGSM49BAMCMIGKMQswCQYDVQQGEwJDTjEQ\nMA4GA1UECBMHQmVpamluZzEQMA4GA1UEBxMHQmVpamluZzEfMB0GA1UEChMWd3gt\nb3JnMy5jaGFpbm1ha2VyLm9yZzESMBAGA1UECxMJcm9vdC1jZXJ0MSIwIAYDVQQD\nExljYS53eC1vcmczLmNoYWlubWFrZXIub3JnMB4XDTIxMDgxMTA4MDMxN1oXDTI2\nMDgxMDA4MDMxN1owgZcxCzAJBgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlqaW5nMRAw\nDgYDVQQHEwdCZWlqaW5nMR8wHQYDVQQKExZ3eC1vcmczLmNoYWlubWFrZXIub3Jn\nMRIwEAYDVQQLEwljb25zZW5zdXMxLzAtBgNVBAMTJmNvbnNlbnN1czEuc2lnbi53\neC1vcmczLmNoYWlubWFrZXIub3JnMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE\nIm5jTPjo3UqmAmlsCGgiYZF4uMDzUGUyaiDrkgsQNfMHj1l+JpbJpBvXFopM9Lg9\nEJSQBbSygNXPQESNuPkM86OBrTCBqjAOBgNVHQ8BAf8EBAMCAaYwDwYDVR0lBAgw\nBgYEVR0lADApBgNVHQ4EIgQgFSdB45UdpkbH8OdxG7QNSLVRm7ETxjaf1F+NymWs\nCZ8wKwYDVR0jBCQwIoAgWFLM1GDIAklQLXYGJBPOUqMRepJOIY5XCb85E57wxjMw\nLwYLgSdYj2QLHo9kCwQEIDg1YTc0NjdkZjQxZjQzMDJhN2IzZTYwZDAxNTYwYTdl\nMAoGCCqGSM49BAMCA0gAMEUCIQCtdZoWkUiCaEm5NRz4/rP+FUkpOLv2gJ0sv0Ro\nAd8lsQIgeEoGsU0aESP79JuhYpRrHJJi+bKWbRX6BUeOfOfgaks=\n-----END CERTIFICATE-----\n\u0012G0E\u0002 \\zÄ«\u001Bôì:<o]#;Þà}Ø@¸\u0013´\u0095ûDøt+\u001C\u0006a<í\u0002!\u0000\u008Boæ ÕÝà!Û® ¾¬ÛÉ¤ÐÔíB°Ã\u0005Jnj\u0012\u000Eî¯î»2ì\t\n.QmVg1t42nqHfdxZXKg4uM9aZwy1zWVrtVZDu8fazP3Qzms\u0012¹\t\b\u0001\u0012.QmVg1t42nqHfdxZXKg4uM9aZwy1zWVrtVZDu8fazP3Qzms\u0018\u0001* ¡/rs\u0004\u001Dà\u0091$«\u0005Ì,\u0092\u0092O\v­_îºâà#M\u00069Èo \u0017a2à\b\n\u0094\b\n\u0016wx-org1.chainmaker.org\u001Aù\a-----BEGIN CERTIFICATE-----\nMIICwTCCAmigAwIBAgIDBTWeMAoGCCqGSM49BAMCMIGKMQswCQYDVQQGEwJDTjEQ\nMA4GA1UECBMHQmVpamluZzEQMA4GA1UEBxMHQmVpamluZzEfMB0GA1UEChMWd3gt\nb3JnMS5jaGFpbm1ha2VyLm9yZzESMBAGA1UECxMJcm9vdC1jZXJ0MSIwIAYDVQQD\nExljYS53eC1vcmcxLmNoYWlubWFrZXIub3JnMB4XDTIxMDgxMTA4MDMxN1oXDTI2\nMDgxMDA4MDMxN1owgZcxCzAJBgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlqaW5nMRAw\nDgYDVQQHEwdCZWlqaW5nMR8wHQYDVQQKExZ3eC1vcmcxLmNoYWlubWFrZXIub3Jn\nMRIwEAYDVQQLEwljb25zZW5zdXMxLzAtBgNVBAMTJmNvbnNlbnN1czEuc2lnbi53\neC1vcmcxLmNoYWlubWFrZXIub3JnMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE\nQemIGImpOUIyb5Fhjg8tjW9fUDeZgZlrYIxwHebkpXxOZiJ49WN1JlR1+XI4gtNZ\nbrpdJ/rxWYg2JdBP9sWqYqOBrTCBqjAOBgNVHQ8BAf8EBAMCAaYwDwYDVR0lBAgw\nBgYEVR0lADApBgNVHQ4EIgQgwPHfZpWX8tJNI/RpPtmDrfeZeOg837K1Pzu11/c0\nHxUwKwYDVR0jBCQwIoAg1L7QsZdZX0X+4+Yh7GEm1WWusFYnp0aqZ01LdvMy1KQw\nLwYLgSdYj2QLHo9kCwQEIGY2M2NkMGU5Y2U3ZjRlMjNhNjNjOTBhYjJhOTM1ZDc2\nMAoGCCqGSM49BAMCA0cAMEQCIEyt4C68trRRWIznsLa4kTB5zq/VYxPBOhFagONG\nrcN6AiBOfirE8Dgx4c6nvDG7qQajOs3sZigmhbAtTFJcN7+nvg==\n-----END CERTIFICATE-----\n\u0012G0E\u0002 q\u0012F2(F\u0092q\u0015Îb\u0012½-¬\u009A­»ð9åbï\u0018M\u001E{i¥q\u000F\u0017\u0002!\u0000\u0084¯Dî«\u0019\u009FS\n\u0014\u009EÑ\vZ@#éYÈ,Â\u009B4\u0000ê\u0002Ës«ö\u0000û2ì\t\n.Qmc85AX1DsTfATteuRNdRkaxJxXErbBYVi9Ln6v8qDrnRd\u0012¹\t\b\u0001\u0012.Qmc85AX1DsTfATteuRNdRkaxJxXErbBYVi9Ln6v8qDrnRd\u0018\u0001* ¡/rs\u0004\u001Dà\u0091$«\u0005Ì,\u0092\u0092O\v­_îºâà#M\u00069Èo \u0017a2à\b\n\u0094\b\n\u0016wx-org4.chainmaker.org\u001Aù\a-----BEGIN CERTIFICATE-----\nMIICwTCCAmigAwIBAgIDAPOlMAoGCCqGSM49BAMCMIGKMQswCQYDVQQGEwJDTjEQ\nMA4GA1UECBMHQmVpamluZzEQMA4GA1UEBxMHQmVpamluZzEfMB0GA1UEChMWd3gt\nb3JnNC5jaGFpbm1ha2VyLm9yZzESMBAGA1UECxMJcm9vdC1jZXJ0MSIwIAYDVQQD\nExljYS53eC1vcmc0LmNoYWlubWFrZXIub3JnMB4XDTIxMDgxMTA4MDMxN1oXDTI2\nMDgxMDA4MDMxN1owgZcxCzAJBgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlqaW5nMRAw\nDgYDVQQHEwdCZWlqaW5nMR8wHQYDVQQKExZ3eC1vcmc0LmNoYWlubWFrZXIub3Jn\nMRIwEAYDVQQLEwljb25zZW5zdXMxLzAtBgNVBAMTJmNvbnNlbnN1czEuc2lnbi53\neC1vcmc0LmNoYWlubWFrZXIub3JnMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE\nBw8gzgNT/p1rmKGKtrznqb3ysOc+3m37402IpIgQF0F8jFVH2gkj9pDJ3SggAb8/\nhBuLxEl5EniI/K3UlsLYiKOBrTCBqjAOBgNVHQ8BAf8EBAMCAaYwDwYDVR0lBAgw\nBgYEVR0lADApBgNVHQ4EIgQgRVTdZzkr/Qph2enPUZ5ghybro12xYaecOMsv9EVL\nIS0wKwYDVR0jBCQwIoAgJOuVXveeHBBEMzB75XxW/Vvh8Z0BQNRW1gPzRj0PPGMw\nLwYLgSdYj2QLHo9kCwQEIGZmYmNiODgyZGVlNDQ5OWU5NjdhZDllMzUzNWZkZWE3\nMAoGCCqGSM49BAMCA0cAMEQCIBgxIqGznSoqH0q2Cm2fa9s2O7LF3DdNphCSClsB\nL4yjAiAKEIlrz2zdaYewbnOJwzET3y0TnAqFnYsKh5Kai/x8KA==\n-----END CERTIFICATE-----\n\u0012G0E\u0002!\u0000ìv£\u00173þ¯÷\u000FW\u0013\u001E@\u009F8}\u009CÙi[£±\u0010ã\u0000\u0013ùx\tI\u0092V\u0002 \u0004np\u0085¯(ú,\u0094!\u0081\u0092Ç;ÿX¥ó\u0084\u0081\u0012\u008C^\u008BÍBõ\u0093dÌÔÈ:\u0080\u001E\n,oS9ycwQd4JEkqwXMLJKSTwutX+664uAjTQY5yG8gF2E=\u0012Ï\u001D\nì\t\n.QmWAmZZmy3o4XnmvbaAyc41W3We44zqhkh2xJQaSJb8TfT\u0012¹\t\b\u0001\u0012.QmWAmZZmy3o4XnmvbaAyc41W3We44zqhkh2xJQaSJb8TfT\u0018\u0001* ¡/rs\u0004\u001Dà\u0091$«\u0005Ì,\u0092\u0092O\v­_îºâà#M\u00069Èo \u0017a2à\b\n\u0094\b\n\u0016wx-org3.chainmaker.org\u001Aù\a-----BEGIN CERTIFICATE-----\nMIICwjCCAmigAwIBAgIDC+3AMAoGCCqGSM49BAMCMIGKMQswCQYDVQQGEwJDTjEQ\nMA4GA1UECBMHQmVpamluZzEQMA4GA1UEBxMHQmVpamluZzEfMB0GA1UEChMWd3gt\nb3JnMy5jaGFpbm1ha2VyLm9yZzESMBAGA1UECxMJcm9vdC1jZXJ0MSIwIAYDVQQD\nExljYS53eC1vcmczLmNoYWlubWFrZXIub3JnMB4XDTIxMDgxMTA4MDMxN1oXDTI2\nMDgxMDA4MDMxN1owgZcxCzAJBgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlqaW5nMRAw\nDgYDVQQHEwdCZWlqaW5nMR8wHQYDVQQKExZ3eC1vcmczLmNoYWlubWFrZXIub3Jn\nMRIwEAYDVQQLEwljb25zZW5zdXMxLzAtBgNVBAMTJmNvbnNlbnN1czEuc2lnbi53\neC1vcmczLmNoYWlubWFrZXIub3JnMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE\nIm5jTPjo3UqmAmlsCGgiYZF4uMDzUGUyaiDrkgsQNfMHj1l+JpbJpBvXFopM9Lg9\nEJSQBbSygNXPQESNuPkM86OBrTCBqjAOBgNVHQ8BAf8EBAMCAaYwDwYDVR0lBAgw\nBgYEVR0lADApBgNVHQ4EIgQgFSdB45UdpkbH8OdxG7QNSLVRm7ETxjaf1F+NymWs\nCZ8wKwYDVR0jBCQwIoAgWFLM1GDIAklQLXYGJBPOUqMRepJOIY5XCb85E57wxjMw\nLwYLgSdYj2QLHo9kCwQEIDg1YTc0NjdkZjQxZjQzMDJhN2IzZTYwZDAxNTYwYTdl\nMAoGCCqGSM49BAMCA0gAMEUCIQCtdZoWkUiCaEm5NRz4/rP+FUkpOLv2gJ0sv0Ro\nAd8lsQIgeEoGsU0aESP79JuhYpRrHJJi+bKWbRX6BUeOfOfgaks=\n-----END CERTIFICATE-----\n\u0012G0E\u0002 \\zÄ«\u001Bôì:<o]#;Þà}Ø@¸\u0013´\u0095ûDøt+\u001C\u0006a<í\u0002!\u0000\u008Boæ ÕÝà!Û® ¾¬ÛÉ¤ÐÔíB°Ã\u0005Jnj\u0012\u000Eî¯î»\nì\t\n.QmVg1t42nqHfdxZXKg4uM9aZwy1zWVrtVZDu8fazP3Qzms\u0012¹\t\b\u0001\u0012.QmVg1t42nqHfdxZXKg4uM9aZwy1zWVrtVZDu8fazP3Qzms\u0018\u0001* ¡/rs\u0004\u001Dà\u0091$«\u0005Ì,\u0092\u0092O\v­_îºâà#M\u00069Èo \u0017a2à\b\n\u0094\b\n\u0016wx-org1.chainmaker.org\u001Aù\a-----BEGIN CERTIFICATE-----\nMIICwTCCAmigAwIBAgIDBTWeMAoGCCqGSM49BAMCMIGKMQswCQYDVQQGEwJDTjEQ\nMA4GA1UECBMHQmVpamluZzEQMA4GA1UEBxMHQmVpamluZzEfMB0GA1UEChMWd3gt\nb3JnMS5jaGFpbm1ha2VyLm9yZzESMBAGA1UECxMJcm9vdC1jZXJ0MSIwIAYDVQQD\nExljYS53eC1vcmcxLmNoYWlubWFrZXIub3JnMB4XDTIxMDgxMTA4MDMxN1oXDTI2\nMDgxMDA4MDMxN1owgZcxCzAJBgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlqaW5nMRAw\nDgYDVQQHEwdCZWlqaW5nMR8wHQYDVQQKExZ3eC1vcmcxLmNoYWlubWFrZXIub3Jn\nMRIwEAYDVQQLEwljb25zZW5zdXMxLzAtBgNVBAMTJmNvbnNlbnN1czEuc2lnbi53\neC1vcmcxLmNoYWlubWFrZXIub3JnMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE\nQemIGImpOUIyb5Fhjg8tjW9fUDeZgZlrYIxwHebkpXxOZiJ49WN1JlR1+XI4gtNZ\nbrpdJ/rxWYg2JdBP9sWqYqOBrTCBqjAOBgNVHQ8BAf8EBAMCAaYwDwYDVR0lBAgw\nBgYEVR0lADApBgNVHQ4EIgQgwPHfZpWX8tJNI/RpPtmDrfeZeOg837K1Pzu11/c0\nHxUwKwYDVR0jBCQwIoAg1L7QsZdZX0X+4+Yh7GEm1WWusFYnp0aqZ01LdvMy1KQw\nLwYLgSdYj2QLHo9kCwQEIGY2M2NkMGU5Y2U3ZjRlMjNhNjNjOTBhYjJhOTM1ZDc2\nMAoGCCqGSM49BAMCA0cAMEQCIEyt4C68trRRWIznsLa4kTB5zq/VYxPBOhFagONG\nrcN6AiBOfirE8Dgx4c6nvDG7qQajOs3sZigmhbAtTFJcN7+nvg==\n-----END CERTIFICATE-----\n\u0012G0E\u0002 q\u0012F2(F\u0092q\u0015Îb\u0012½-¬\u009A­»ð9åbï\u0018M\u001E{i¥q\u000F\u0017\u0002!\u0000\u0084¯Dî«\u0019\u009FS\n\u0014\u009EÑ\vZ@#éYÈ,Â\u009B4\u0000ê\u0002Ës«ö\u0000û\nì\t\n.Qmc85AX1DsTfATteuRNdRkaxJxXErbBYVi9Ln6v8qDrnRd\u0012¹\t\b\u0001\u0012.Qmc85AX1DsTfATteuRNdRkaxJxXErbBYVi9Ln6v8qDrnRd\u0018\u0001* ¡/rs\u0004\u001Dà\u0091$«\u0005Ì,\u0092\u0092O\v­_îºâà#M\u00069Èo \u0017a2à\b\n\u0094\b\n\u0016wx-org4.chainmaker.org\u001Aù\a-----BEGIN CERTIFICATE-----\nMIICwTCCAmigAwIBAgIDAPOlMAoGCCqGSM49BAMCMIGKMQswCQYDVQQGEwJDTjEQ\nMA4GA1UECBMHQmVpamluZzEQMA4GA1UEBxMHQmVpamluZzEfMB0GA1UEChMWd3gt\nb3JnNC5jaGFpbm1ha2VyLm9yZzESMBAGA1UECxMJcm9vdC1jZXJ0MSIwIAYDVQQD\nExljYS53eC1vcmc0LmNoYWlubWFrZXIub3JnMB4XDTIxMDgxMTA4MDMxN1oXDTI2\nMDgxMDA4MDMxN1owgZcxCzAJBgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlqaW5nMRAw\nDgYDVQQHEwdCZWlqaW5nMR8wHQYDVQQKExZ3eC1vcmc0LmNoYWlubWFrZXIub3Jn\nMRIwEAYDVQQLEwljb25zZW5zdXMxLzAtBgNVBAMTJmNvbnNlbnN1czEuc2lnbi53\neC1vcmc0LmNoYWlubWFrZXIub3JnMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE\nBw8gzgNT/p1rmKGKtrznqb3ysOc+3m37402IpIgQF0F8jFVH2gkj9pDJ3SggAb8/\nhBuLxEl5EniI/K3UlsLYiKOBrTCBqjAOBgNVHQ8BAf8EBAMCAaYwDwYDVR0lBAgw\nBgYEVR0lADApBgNVHQ4EIgQgRVTdZzkr/Qph2enPUZ5ghybro12xYaecOMsv9EVL\nIS0wKwYDVR0jBCQwIoAgJOuVXveeHBBEMzB75XxW/Vvh8Z0BQNRW1gPzRj0PPGMw\nLwYLgSdYj2QLHo9kCwQEIGZmYmNiODgyZGVlNDQ5OWU5NjdhZDllMzUzNWZkZWE3\nMAoGCCqGSM49BAMCA0cAMEQCIBgxIqGznSoqH0q2Cm2fa9s2O7LF3DdNphCSClsB\nL4yjAiAKEIlrz2zdaYewbnOJwzET3y0TnAqFnYsKh5Kai/x8KA==\n-----END CERTIFICATE-----\n\u0012G0E\u0002!\u0000ìv£\u00173þ¯÷\u000FW\u0013\u001E@\u009F8}\u009CÙi[£±\u0010ã\u0000\u0013ùx\tI\u0092V\u0002 \u0004np\u0085¯(ú,\u0094!\u0081\u0092Ç;ÿX¥ó\u0084\u0081\u0012\u008C^\u008BÍBõ\u0093dÌÔÈ\u0010\u0003\u0012\u0081\t\n@98d32153edcb4e7393044c8c9ecb5bd1d50534a23c1247f783d79727c2eaad2e\u0012<\n\u0014Contract:CERT_MANAGE\u0012\u0013\n\vCERT_MANAGE\u0012\u0002v1\u0018\u0001\u001A\u000FCONTRACT_MANAGE\u001Aþ\a\n@fd7ce8e8f61ae8f831fd8144ac5b98aeacd84100671d1293af70f3464772b3f3\u0012¬\a-----BEGIN CERTIFICATE-----\nMIICijCCAi+gAwIBAgIDDXisMAoGCCqGSM49BAMCMIGKMQswCQYDVQQGEwJDTjEQ\nMA4GA1UECBMHQmVpamluZzEQMA4GA1UEBxMHQmVpamluZzEfMB0GA1UEChMWd3gt\nb3JnMS5jaGFpbm1ha2VyLm9yZzESMBAGA1UECxMJcm9vdC1jZXJ0MSIwIAYDVQQD\nExljYS53eC1vcmcxLmNoYWlubWFrZXIub3JnMB4XDTIxMDgxMTA4MDMxN1oXDTI2\nMDgxMDA4MDMxN1owgZExCzAJBgNVBAYTAkNOMRAwDgYDVQQIEwdCZWlqaW5nMRAw\nDgYDVQQHEwdCZWlqaW5nMR8wHQYDVQQKExZ3eC1vcmcxLmNoYWlubWFrZXIub3Jn\nMQ8wDQYDVQQLEwZjbGllbnQxLDAqBgNVBAMTI2NsaWVudDEuc2lnbi53eC1vcmcx\nLmNoYWlubWFrZXIub3JnMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEQBZcw4AT\nFTGl16tFYzZ2cuemLVABI61aS3CeHbUoaAarumE57M/Yv2POtjiNhlk4GS8YZUHU\nKob7G2DP1wI056N7MHkwDgYDVR0PAQH/BAQDAgGmMA8GA1UdJQQIMAYGBFUdJQAw\nKQYDVR0OBCIEINrcPwy5PcHjLsfvdeukukpo1gvqOV8Bxj8Ll/oRlmGaMCsGA1Ud\nIwQkMCKAINS+0LGXWV9F/uPmIexhJtVlrrBWJ6dGqmdNS3bzMtSkMAoGCCqGSM49\nBAMCA0kAMEYCIQDgp1sdA+1pQy6w3qQPHumSuIB4+BormFZu3IiYCOBWYQIhAKb/\nkiWekJhlhUVOgglNWlYlp3A8+8fFv0UKl39xRMi8\n-----END CERTIFICATE-----\n\u001A\vCERT_MANAGE"),
			&common.TxResponse{Code: common.TxStatusCode_SUCCESS},
			nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cli, err := newMockChainClient(tt.serverTxResp, tt.serverErr, WithConfPath(sdkConfigPathForUT))
			require.Nil(t, err)
			defer cli.Stop()

			var (
				payload *common.Payload
				resp    *common.TxResponse
			)

			payload, err = cli.CreateRestoreBlockPayload(tt.fullblock)
			require.Nil(t, err)

			resp, err = cli.SendRestoreBlockRequest(payload, -1)
			require.Equal(t, tt.serverErr, err)
			require.Equal(t, tt.serverTxResp.Code, resp.Code)
		})
	}
}
